package main

import (
	"fmt"
	"log"

	"github.com/homewizeAI/apicommon"
	"github.com/homewizeAI/apicommongin"
	"github.com/homewizeAI/authms/api"
	"github.com/homewizeAI/dbutil"
)

func main() {
	// Initialize database
	dbutil.Init()
	defer dbutil.Close()

	// Create Gin router
	r := apicommongin.NewRouter()

	// Password routes
	g := r.Group("/auth")

	g.POST("/pwd", api.PwdCreate)       // POST /password
	g.PUT("/pwd/:id", api.PwdUpdate)    // PUT /password/:id
	g.DELETE("/pwd/:id", api.PwdDelete) // DELETE /password/:id

	g.POST("/pwd/validate", api.PwdValidate)            // POST /password/validate
	g.POST("/pwd/check-strength", api.PwdCheckStrength) // POST /password/check-strength

	// Password reset routes
	g.POST("/pwd/reset", api.PwdResetCreate)                            // POST /pwd/reset
	g.POST("/pwd/reset/key", api.PwdResetKeyGet)                        // POST /pwd/reset/key
	g.PUT("/pwd/reset/update", api.PwdResetUpdate)                      // PUT /pwd/reset/update
	g.DELETE("/pwd/reset", api.PwdResetDelete)                          // DELETE /pwd/reset
	g.GET("/pwd/reset/has-active-key/:usrID", api.PwdResetHasActiveKey) // GET /pwd/reset/has-active-key/:usrID

	// Token routes
	g.POST("/pwd/reset/validate", api.PwdResetValidate) // POST /pwd/reset/validate

	g.POST("/token/refresh", api.RefreshToken) // POST /token/refresh
	g.DELETE("/token/refresh", api.RevokeRToken)
	g.POST("/token/belongs-to", api.RefreshTokenBelongsTo)
	g.DELETE("/token/refresh/user/:id", api.RTokenDelByUser)
	g.POST("/token/refresh/fetch-acc-usr", api.RTokenFetchAccUsr)

	// Start server
	port := apicommon.CliPort(3001) // Default port is 3001 (different from accmgtms)
	portStr := fmt.Sprintf(":%d", port)
	log.Printf("Starting server on %s", portStr)
	if err := r.Run(portStr); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}
