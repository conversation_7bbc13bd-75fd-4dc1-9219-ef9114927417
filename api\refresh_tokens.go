package api

import (
	"github.com/gin-gonic/gin"
	"github.com/homewizeAI/apicommon"
	"github.com/homewizeAI/apitypes"
	"github.com/homewizeAI/authms/cfg"
	"github.com/homewizeAI/authms/dao"
	"github.com/homewizeAI/authms/model"
	"github.com/homewizeAI/errs"
	"github.com/homewizeAI/utils"
)

func RefreshTokenBelongsTo(c *gin.Context) {
	var err error
	var req model.RefreshTokenBelongsToReq
	if err := c.BindJSON(&req); err != nil {
		apicommon.RespBadRequest(c, "payload")
		return
	}

	belongs, err := dao.RefreshTokenBelongsTO(req)
	if err != nil {
		apicommon.RespISE(c, "belogs-to")
		return
	}
	apicommon.RespOk(c, apitypes.BoolTypeNew(belongs))
}

// RefreshToken refreshes JWT token using refresh token
func RefreshToken(c *gin.Context) {
	var req model.RefreshTokenRequest
	if err := c.BindJSON(&req); err != nil {
		apicommon.RespBadRequest(c, "payload")
		return
	}

	usrID, accID, err := dao.RefreshTokenGetByToken(req.RefreshToken)
	if err != nil {
		if errs.IsNotFound(err) || errs.IsBadInput(err) {
			apicommon.RespNotFound(c, "invalid-refresh-token")
			return
		}
		apicommon.RespISE(c, "token-refresh-error")
		return
	}
	newToken, err := dao.RenewRefreshToken(req.RefreshToken)
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, "Renew-refresh-token")
		return
	}
	out, err := getRTokenRes(usrID, accID, newToken)
	if err != nil {
		apicommon.RespISE(c, "get-rtoken-res")
		return
	}

	apicommon.RespOk(c, out)
}

func RevokeRToken(c *gin.Context) {
	var req model.RTokenRevokeReq
	if err := c.BindJSON(&req); err != nil {
		apicommon.RespBadRequest(c, "payload")
		return
	}
	if affRows, err := dao.RefreshTokenDeleteByToken(req.RToken); err != nil {
		apicommon.RespISE(c, "revoke-token")
	} else {
		apicommon.RespOk(c, apitypes.AffectedRowsTypeNew(affRows))
	}
}

func RTokenDelByUser(c *gin.Context) {
	usrID, err := utils.AsUint64(c.Param("id"))
	if err != nil {
		apicommon.RespBadRequest(c, "id")
		return
	}
	affRows, err := dao.RefreshTokenDeleteByUserID(usrID)
	if err != nil {
		apicommon.RespISE(c, "delete-token-by-user")
		return
	}

	apicommon.RespOk(c, apitypes.AffectedRowsTypeNew(affRows))
}

func getRTokenRes(usrID, accID uint64, newRToken string) (model.RTokenRenewRes, error) {
	var out model.RTokenRenewRes
	// Get JWT manager
	jwtManager := cfg.AuthConfig.GetJWTManager()

	// Generate JWT token with user ID and account ID
	accessToken, err := jwtManager.GenerateToken(usrID, accID, cfg.AuthConfig.JWT.ExpirationMinutes)
	if err != nil {
		return out, err
	}

	out.Access = accessToken
	out.Refresh = newRToken
	return out, nil
}

func getPwdValidateRTokenRes(userID, accID uint64) (model.Token, error) {
	// Get JWT manager
	jwtManager := cfg.AuthConfig.GetJWTManager()
	var out model.Token
	// Generate JWT token with user ID and account ID
	accessToken, err := jwtManager.GenerateToken(userID, accID, cfg.AuthConfig.JWT.ExpirationMinutes)
	if err != nil {
		return out, err
	}

	// Generate refresh token
	// Store in database
	refreshToken, err := dao.RefreshTokenCreate(userID, accID)
	if err != nil {
		return out, err
	}

	out.Access = accessToken
	out.Refresh = refreshToken

	return out, nil
}

func RTokenFetchAccUsr(c *gin.Context) {
	var req model.RTokenFetchAccUsrReq
	if err := c.BindJSON(&req); err != nil {
		apicommon.RespBadRequest(c, "payload")
		return
	}
	usrID, accID, err := dao.RefreshTokenGetByToken(req.RToken)
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, "fetch-acc-usr")
		return
	}

	out := model.RTokenFetchAccUsrRes{
		UsrID: usrID,
		AccID: accID,
	}

	apicommon.RespOk(c, out)
}
