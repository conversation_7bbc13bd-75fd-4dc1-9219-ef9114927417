#!/bin/bash

# Simple build script for Authentication Management Service

set -e

echo "🔨 Building Authentication Management Service..."

# Check if Go is installed
if ! command -v go &> /dev/null; then
    echo "❌ Go is not installed. Please install Go first."
    exit 1
fi

# Download dependencies
echo "📦 Downloading dependencies..."
go mod tidy

# Verify build (without creating binary)
echo "🔨 Verifying build..."
go build -o /dev/null main.go

echo "✅ Build verification successful!"
echo "🚀 Run with: go run main.go"
