# Build stage
FROM golang:1.23-alpine AS builder

# Set GOTOOLCHAIN to auto to allow Go to download the required version
ENV GOTOOLCHAIN=auto

# Install git (required for Go modules)
RUN apk add --no-cache git

RUN mkdir /workdir
COPY . /workdir
# Set working directory
WORKDIR /workdir

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o authms

# Final stage
FROM alpine:latest

RUN addgroup -g 1001 homewize
RUN adduser -u 1001 -G homewize --disabled-password --no-create-home homewize

WORKDIR /app
RUN chown -R homewize:homewize /app

USER homewize

COPY --from=builder --chown=homewize:homewize /workdir/authms .
COPY --from=builder --chown=homewize:homewize /workdir/docker/entrypoint.sh .

# Expose port
EXPOSE 3001

# Run the application
ENTRYPOINT ["./entrypoint.sh"]
