#!/bin/bash

# Test runner script for authms
# Usage: ./run-tests.sh [unit|integration|client|all]

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Parse command line arguments
TEST_TYPE=${1:-all}

if [[ "$TEST_TYPE" != "unit" && "$TEST_TYPE" != "integration" && "$TEST_TYPE" != "client" && "$TEST_TYPE" != "core" && "$TEST_TYPE" != "all" ]]; then
    echo "Usage: $0 [unit|integration|client|core|all]"
    echo "  unit        - Run unit tests (DAO and core logic)"
    echo "  integration - Run integration tests (API tests)"
    echo "  client      - Run client library tests"
    echo "  core        - Run core logic tests"
    echo "  all         - Run all tests (default)"
    exit 1
fi

# Check if Go is available
if ! command -v go &> /dev/null; then
    print_error "Go is not installed or not in PATH"
    exit 1
fi

print_info "Running authms tests..."

# Function to run unit tests
run_unit_tests() {
    print_info "Running unit tests (DAO and core)..."
    if go test ./tests -v -run "TestPwd|TestRefreshToken" -tags=unit; then
        print_success "Unit tests passed!"
    else
        print_error "Unit tests failed!"
        return 1
    fi
}

# Function to run integration tests
run_integration_tests() {
    print_info "Running integration tests (API)..."
    if go test ./tests -v -run "TestAPI" -tags=integration; then
        print_success "Integration tests passed!"
    else
        print_error "Integration tests failed!"
        return 1
    fi
}

# Function to run client tests
run_client_tests() {
    print_info "Running client library tests..."
    if go test ./client -v; then
        print_success "Client tests passed!"
    else
        print_error "Client tests failed!"
        return 1
    fi
}

# Function to run core tests
run_core_tests() {
    print_info "Running core tests (JWT and refresh token logic)..."
    if go test ./core -v; then
        print_success "Core tests passed!"
    else
        print_error "Core tests failed!"
        return 1
    fi
}

# Main test execution
case "$TEST_TYPE" in
    "unit")
        run_unit_tests
        run_core_tests
        ;;
    "integration")
        run_integration_tests
        ;;
    "client")
        run_client_tests
        ;;
    "core")
        run_core_tests
        ;;
    "all")
        print_info "Running all test suites..."
        
        # Run core tests first
        run_core_tests
        
        # Run client tests
        run_client_tests
        
        # Note: Unit and integration tests require database setup
        print_warning "Unit and integration tests require database setup"
        print_info "To run database tests:"
        print_info "1. Setup PostgreSQL with authms_test database"
        print_info "2. Run: go test ./tests -v"
        
        print_success "All available tests completed successfully!"
        ;;
esac

print_success "Test execution completed!"
