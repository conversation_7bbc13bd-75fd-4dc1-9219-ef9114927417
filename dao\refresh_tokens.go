package dao

import (
	"database/sql"
	"strings"
	"time"

	"github.com/homewizeAI/authms/model"
	"github.com/homewizeAI/dbutil"
	"github.com/homewizeAI/errs"
	"github.com/homewizeAI/keylib"
	"github.com/homewizeAI/utils"
)

// RefreshTokenCreate creates a new refresh token record
func RefreshTokenCreate(userID, accID uint64) (string, error) {
	rToken := keylib.NewSecureKey64()
	query := `
		INSERT INTO refresh_tokens (token, user_id, acc_id, valid_from)
		VALUES ($1, $2, $3, $4)
		RETURNING id`

	var id uint64
	err := dbutil.QueryRow(query, rToken, userID, accID, time.Now().UTC()).Scan(&id)
	if err != nil {
		if dbutil.ErrIsFKChildViolation(err) {
			return "", errs.BadInput("relationship")
		}
		if dbutil.ErrIsUniqueViolation(err) {
			return "", errs.BadInput("token-exists")
		}
		return "", err
	}

	return buildToken(id, rToken), nil
}

func buildToken(id uint64, rToken string) string {
	return rToken + "::" + utils.AsString(id)
}

func splitToken(rToken string) (uint64, string, error) {
	var id uint64
	var token string
	var err error
	lastIndex := strings.LastIndex(rToken, "::")
	if lastIndex == -1 {
		return 0, "", errs.NotFound("rToken")
	} else {
		token = rToken[0:lastIndex]
		idStr := rToken[lastIndex+2:]
		id, err = utils.AsUint64(idStr)
		if err != nil {
			return 0, "", errs.NotFound("rToken")
		}
	}
	return id, token, nil
}

// RefreshTokenGetByToken retrieves a refresh token by its token value
func RefreshTokenGetByToken(token string) (uint64, uint64, error) {
	_, rToken, err := splitToken(token)
	if err != nil {
		return 0, 0, errs.NotFound("rToken")
	}
	query := `
		SELECT user_id, acc_id
		FROM refresh_tokens
		WHERE token = $1
		LIMIT 1`

	var usrID, accID uint64
	err = dbutil.QueryRow(query, rToken).Scan(&usrID, &accID)
	if err != nil {
		if err == sql.ErrNoRows {
			return usrID, accID, errs.NotFound("refresh-token")
		}
		return usrID, accID, err
	}

	return usrID, accID, nil
}

// RefreshTokenDeleteByToken deletes a refresh token by its token value
func RefreshTokenDeleteByToken(token string) (uint64, error) {
	id, rToken, err := splitToken(token)
	if err != nil {
		return 0, nil // Invalid token format should return 0 affected rows, not error
	}
	query := `DELETE FROM refresh_tokens WHERE token = $1 and id = $2`

	rowsAffected, err := dbutil.ExecGetAffRows(query, rToken, id)
	if err != nil {
		return 0, err
	}

	return rowsAffected, nil
}

// RefreshTokenDeleteByUserID deletes all refresh tokens for a specific user
func RefreshTokenDeleteByUserID(userID uint64) (uint64, error) {
	query := `DELETE FROM refresh_tokens WHERE user_id = $1`

	rowsAffected, err := dbutil.ExecGetAffRows(query, userID)
	if err != nil {
		return 0, err
	}

	return rowsAffected, nil
}

// RefreshTokenGetByUserID retrieves all refresh tokens for a user
func RefreshTokenGetByUserID(userID uint64) ([]model.RefreshToken, error) {
	query := `
		SELECT id, token, user_id, acc_id, valid_from
		FROM refresh_tokens
		WHERE user_id = $1
		ORDER BY valid_from DESC`

	rows, err := dbutil.Query(query, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var tokens []model.RefreshToken
	for rows.Next() {
		var rt model.RefreshToken
		err := rows.Scan(
			&rt.ID, &rt.Token, &rt.UserID, &rt.AccID, &rt.ValidFrom,
		)
		if err != nil {
			return nil, err
		}
		tokens = append(tokens, rt)
	}

	if err = rows.Err(); err != nil {
		return nil, err
	}

	return tokens, nil
}

func RenewRefreshToken(token string) (newToken string, err error) {
	newToken = keylib.NewSecureKey64()
	id, rToken, err := splitToken(token)
	if err != nil {
		return "", errs.NotFound("rToken")
	}

	hasExpired, err := rTokenHasExpired(id, rToken)
	if err != nil {
		return "", err
	}
	if hasExpired {
		_, err = RefreshTokenDeleteByToken(token)
		if err != nil {
			return "", err
		}
		return "", errs.NotFound("rToken-expired")
	}
	var qry string
	var affRows uint64
	qry = `update refresh_tokens set token = $1 where token = $2 and id = $3 `
	affRows, err = dbutil.ExecGetAffRows(qry, newToken, rToken, id)
	if err != nil {
		return "", err
	}
	if affRows == 0 {
		return "", errs.NotFound("rToken")
	}
	return buildToken(id, newToken), nil
}

func rTokenHasExpired(id uint64, rToken string) (bool, error) {
	qry := `select valid_from from refresh_tokens where id = $1 and token = $2`
	row := dbutil.QueryRow(qry, id, rToken)
	var from *time.Time
	err := row.Scan(&from)
	if err != nil {
		if err == sql.ErrNoRows {
			return false, errs.NotFound("rToken")
		}
		return false, err
	}
	if from == nil {
		return false, nil
	}
	ttlInHours, err := rTokenExpiryGet()
	if err != nil {
		return false, err
	}
	curTime := time.Now().UTC()
	if curTime.Before(*from) {
		return false, nil
	}

	hours := curTime.Sub(*from).Hours()
	if hours >= float64(ttlInHours) {
		return true, nil
	}
	return false, nil
}

func rTokenExpiryGet() (uint64, error) {
	return utils.AsUint64("24")
}

func RefreshTokenBelongsTO(req model.RefreshTokenBelongsToReq) (bool, error) {
	id, rToken, err := splitToken(req.RToken)
	if err != nil {
		return false, nil // Invalid token format should return false, not error
	}

	qry := `select 1 from refresh_tokens where token=$1 and id=$2 and acc_id = $3 and user_id = $4`
	row := dbutil.QueryRow(qry, rToken, id, req.AccID, req.UsrID)
	var out int
	err = row.Scan(&out)
	if err != nil {
		if err == sql.ErrNoRows {
			return false, nil
		}
		return false, err
	}

	return true, nil
}
