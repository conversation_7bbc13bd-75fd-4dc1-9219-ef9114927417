package dao

import (
	"database/sql"
	"log"
	"time"

	"github.com/homewizeAI/authms/model"
	"github.com/homewizeAI/dbutil"
	"github.com/homewizeAI/errs"
	"github.com/homewizeAI/keylib"
)

const (
	pwdResetKeyExpiryTimeInHours    = 48
	pwdResetKeyRenewWaitTimeInHours = 12
)

// PwdResetCreate creates a new password reset record
func PwdResetCreate(data model.PwdResetCreateRequest) (model.PwdResetCreateResponse, error) {
	resetKey := keylib.NewSecureKey64()
	var out model.PwdResetCreateResponse

	pwdRow, err := PwdGetByUsrID(data.UsrID)
	if err != nil {
		if errs.IsNotFound(err) {
			return out, errs.BadInput("user-password-not-found")
		}
		return out, err
	}

	qry := `update pwd_resets set reset_key = $1, updated_at = $2 where pwd_id = $3`
	affRows, err := dbutil.ExecGetAffRows(qry, resetKey, time.Now().UTC(), pwdRow.ID)
	if err != nil {
		return out, err
	}

	if affRows == 1 {
		// Check if there's already an active reset for this password
		qry := `select id from pwd_resets where pwd_id = $1`
		row := dbutil.QueryRow(qry, pwdRow.ID)
		var existingID uint64
		err = row.Scan(&existingID)
		if err != nil {
			return out, err
		}
		out.ResetKey = resetKey
		out.ID = existingID
		return out, nil
	}

	query := `
		INSERT INTO pwd_resets (
			reset_key,
			pwd_id,
			created_at,
			updated_at
		) VALUES (
			$1, $2, $3, $4
		)
		RETURNING id
	`

	var id uint64
	err = dbutil.QueryRow(query, resetKey, pwdRow.ID, time.Now().UTC(), time.Now().UTC()).Scan(&id)
	if err != nil {
		if dbutil.ErrIsFKChildViolation(err) {
			return out, errs.BadInput("invalid-password-id")
		}
		return out, err
	}

	return model.PwdResetCreateResponse{
		ResetKey: resetKey,
		ID:       id,
	}, nil
}

// PwdResetGetByKey retrieves a password reset by reset key
func PwdResetGetByKey(resetKey string) (*model.PwdReset, error) {
	query := `
		SELECT id, reset_key, pwd_id, created_at, updated_at 
		FROM pwd_resets 
		WHERE reset_key = $1 
		LIMIT 1
	`

	var reset model.PwdReset
	err := dbutil.QueryRow(query, resetKey).Scan(
		&reset.ID,
		&reset.ResetKey,
		&reset.PwdID,
		&reset.CreatedAt,
		&reset.UpdatedAt,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errs.NotFound("reset-key")
		}
		return nil, err
	}

	return &reset, nil
}

// PwdResetDelete deletes a password reset by ID
func PwdResetDelete(id uint64, resetKey string) (uint64, error) {
	query := `DELETE FROM pwd_resets WHERE id = $1 and reset_key = $2`

	rowsAffected, err := dbutil.ExecGetAffRows(query, id, resetKey)
	if err != nil {
		return 0, err
	}

	return rowsAffected, nil
}

// PwdResetDeleteByPwdID deletes all password resets for a password ID
func PwdResetDeleteByPwdID(pwdID uint64) (uint64, error) {
	query := `DELETE FROM pwd_resets WHERE pwd_id = $1`

	rowsAffected, err := dbutil.ExecGetAffRows(query, pwdID)
	if err != nil {
		return 0, err
	}

	return rowsAffected, nil
}

func PwdResetKeyGet(req model.PwdResetKeyGetRequest) (model.PwdResetKeyGetResponse, error) {
	var out model.PwdResetKeyGetResponse
	var updated_at time.Time
	qry := ` select p.acc_id, p.usr_id, pr.updated_at from pwds p join pwd_resets pr on p.id = pr.pwd_id where pr.reset_key = $1 and pr.id = $2`
	row := dbutil.QueryRow(qry, req.ResetKey, req.ID)
	err := row.Scan(&out.AccID, &out.UsrID, &updated_at)
	if err != nil {
		if sql.ErrNoRows == err {
			return out, errs.NotFound("reset-key")
		}
		return model.PwdResetKeyGetResponse{}, err
	}
	if hasTimeLimitExceeded(updated_at, pwdResetKeyRenewWaitTimeInHours) {
		return out, errs.NotFound("reset-key")
	}
	return out, nil
}

func hasTimeLimitExceeded(updatedAt time.Time, limitInHours int) bool {
	return time.Since(updatedAt).Seconds() > float64(limitInHours*3600)
}

func PwdGetIDByResetKey(id uint64, resetKey string) (uint64, error) {
	qry := `select pwd_id from pwd_resets where id = $1 and reset_key = $2`
	row := dbutil.QueryRow(qry, id, resetKey)
	var pwdID uint64
	err := row.Scan(&pwdID)
	if err != nil {
		if sql.ErrNoRows == err {
			return 0, errs.NotFound("reset-key")
		}
		return 0, err
	}
	return pwdID, nil
}

func PwdResetUpdate(req model.PwdResetUpdateRequest) (uint64, error) {
	pwdID, err := PwdGetIDByResetKey(req.ID, req.ResetKey)
	if err != nil {
		return 0, err
	}
	pwdHash, err := keylib.GenerateHashWithDefaults(req.Pwd)
	if err != nil {
		return 0, err
	}

	qry := ` update pwds set pwd=$1 where id = $2`
	affRows, err := dbutil.ExecGetAffRows(qry, pwdHash, pwdID)
	if err != nil {
		return 0, err
	}

	if affRows == 0 {
		log.Println("PwdResetUpdate: pwdID not found", pwdID)
	}

	affRows, err = PwdResetDelete(req.ID, req.ResetKey)
	if err != nil {
		return 0, err
	}

	return affRows, nil
}

func PwdResetHasActiveKey(usrID uint64) (bool, error) {
	pwd, err := PwdGetByUsrID(usrID)
	if err != nil {
		return false, err
	}

	qry := `select updated_at from pwd_resets where pwd_id = $1`
	row := dbutil.QueryRow(qry, pwd.ID)
	var updatedAt time.Time
	err = row.Scan(&updatedAt)
	if err != nil {
		if sql.ErrNoRows == err {
			return false, nil
		}
		return false, err
	}
	return !hasTimeLimitExceeded(updatedAt, pwdResetKeyRenewWaitTimeInHours), nil
}
