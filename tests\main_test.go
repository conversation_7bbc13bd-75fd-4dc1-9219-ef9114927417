package tests

import (
	"fmt"
	"net/http/httptest"
	"os"
	"testing"

	"github.com/gavv/httpexpect/v2"
	"github.com/gin-gonic/gin"
	_ "github.com/lib/pq"

	"github.com/homewizeAI/apicommongin"
	"github.com/homewizeAI/authms/api"
	"github.com/homewizeAI/authms/cfg"
	"github.com/homewizeAI/authms/model"
	"github.com/homewizeAI/dbutil"
)

var (
	server *httptest.Server
)

// TestMain sets up the test environment
func TestMain(m *testing.M) {
	// Change to parent directory to find conf/secrets.toml
	if err := os.Chdir(".."); err != nil {
		panic(fmt.Sprintf("Failed to change directory: %v", err))
	}

	// Set environment variable for database password
	os.Setenv("PGPASSWORD", "postgres")

	// Initialize database using dbutil
	dbutil.Init()
	defer dbutil.Close()

	// Initialize auth config (now we're in the parent directory, so conf/authkeys.toml should work)
	cfg.Init()

	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Create test router with same routes as main.go
	router := setupTestRouter()

	// Create test server
	server = httptest.NewServer(router)
	defer server.Close()

	// Run tests
	code := m.Run()

	// Clean up any remaining test data
	cleanupTestData()

	os.Exit(code)
}

// getExpect returns an httpexpect instance for the given test
func getExpect(t *testing.T) *httpexpect.Expect {
	return httpexpect.Default(t, server.URL)
}

// setupTestRouter creates the same router configuration as main.go
func setupTestRouter() *gin.Engine {
	r := apicommongin.NewRouter()

	// Password routes
	g := r.Group("/auth")

	g.POST("/pwd", api.PwdCreate)       // POST /password
	g.PUT("/pwd/:id", api.PwdUpdate)    // PUT /password/:id
	g.DELETE("/pwd/:id", api.PwdDelete) // DELETE /password/:id

	g.POST("/pwd/validate", api.PwdValidate)            // POST /password/validate
	g.POST("/pwd/check-strength", api.PwdCheckStrength) // POST /password/check-strength

	// Password reset routes
	g.POST("/pwd/reset", api.PwdResetCreate)                            // POST /pwd/reset
	g.POST("/pwd/reset/key", api.PwdResetKeyGet)                        // POST /pwd/reset/key
	g.PUT("/pwd/reset/update", api.PwdResetUpdate)                      // PUT /pwd/reset/update
	g.DELETE("/pwd/reset", api.PwdResetDelete)                          // DELETE /pwd/reset
	g.GET("/pwd/reset/has-active-key/:usrID", api.PwdResetHasActiveKey) // GET /pwd/reset/has-active-key/:usrID

	// Token routes
	g.POST("/pwd/reset/validate", api.PwdResetValidate) // POST /pwd/reset/validate

	g.POST("/token/refresh", api.RefreshToken) // POST /token/refresh
	g.DELETE("/token/refresh", api.RevokeRToken)
	g.POST("/token/belongs-to", api.RefreshTokenBelongsTo)
	g.DELETE("/token/refresh/user/:id", api.RTokenDelByUser)
	g.POST("/token/refresh/fetch-acc-usr", api.RTokenFetchAccUsr)

	return r
}

// Test data cleanup utilities
type TestData struct {
	PasswordIDs     []uint64
	PwdResetIDs     []uint64
	RefreshTokenIDs []uint64
}

var testData = &TestData{}

// Helper function to clean up test data
func cleanupTestData() {
	db := dbutil.GetDB()

	// Clean up in reverse order of dependencies
	for _, id := range testData.RefreshTokenIDs {
		db.Exec("DELETE FROM refresh_tokens WHERE id = $1", id)
	}

	for _, id := range testData.PwdResetIDs {
		db.Exec("DELETE FROM pwd_resets WHERE id = $1", id)
	}

	for _, id := range testData.PasswordIDs {
		db.Exec("DELETE FROM passwords WHERE id = $1", id)
	}

	// Reset test data
	testData = &TestData{}
}

// Helper function to create test password
func createTestPassword(usrID, accID uint64) model.Password {
	return model.Password{
		Pwd:   "TestPassword123!",
		AccID: accID,
		UsrID: usrID,
	}
}

// Helper function to create test password reset request
func createTestPwdResetRequest(usrID uint64) model.PwdResetCreateRequest {
	return model.PwdResetCreateRequest{
		UsrID: usrID,
	}
}

// Helper function to create test password validation request
func createTestPwdValidateRequest(usrID uint64, password string) model.PwdValidateRequest {
	return model.PwdValidateRequest{
		UsrID:    usrID,
		Password: password,
	}
}

// Helper function to create test password strength request
func createTestPwdStrengthRequest(password string) model.PwdStrengthRequest {
	return model.PwdStrengthRequest{
		Password: password,
	}
}

// Helper function to create test refresh token request
func createTestRefreshTokenRequest(refreshToken string) model.RefreshTokenRequest {
	return model.RefreshTokenRequest{
		RefreshToken: refreshToken,
	}
}

// Helper function to create test refresh token belongs to request
func createTestRefreshTokenBelongsToRequest(usrID, accID uint64, rToken string) model.RefreshTokenBelongsToReq {
	req := model.RefreshTokenBelongsToReq{
		UsrID: usrID,
		AccID: accID,
	}
	req.RToken = rToken
	return req
}

// Helper function to create test refresh token revoke request
func createTestRTokenRevokeRequest(rToken string) model.RTokenRevokeReq {
	req := model.RTokenRevokeReq{}
	req.RToken = rToken
	return req
}

// Helper function to create test refresh token fetch acc usr request
func createTestRTokenFetchAccUsrRequest(rToken string) model.RTokenFetchAccUsrReq {
	req := model.RTokenFetchAccUsrReq{}
	req.RToken = rToken
	return req
}

// Helper function to create test password reset key get request
func createTestPwdResetKeyGetRequest(resetKey string, id uint64) model.PwdResetKeyGetRequest {
	return model.PwdResetKeyGetRequest{
		ResetKey: resetKey,
		ID:       id,
	}
}

// Helper function to create test password reset update request
func createTestPwdResetUpdateRequest(resetKey string, id uint64, pwd string) model.PwdResetUpdateRequest {
	return model.PwdResetUpdateRequest{
		ResetKey: resetKey,
		ID:       id,
		Pwd:      pwd,
	}
}

// Helper function to create test password reset delete request
func createTestPwdResetDeleteRequest(resetKey string, id uint64) model.PwdResetDeleteRequest {
	return model.PwdResetDeleteRequest{
		ResetKey: resetKey,
		ID:       id,
	}
}

// Helper function to create test password reset validate request
func createTestPwdResetValidateRequest(resetKey string) model.PwdResetValidateRequest {
	return model.PwdResetValidateRequest{
		ResetKey: resetKey,
	}
}

// Test constants
const (
	TestUserID    = uint64(999999)
	TestAccountID = uint64(888888)
	TestPassword  = "TestPassword123!"
)
