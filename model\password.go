package model

import (
	"time"
)

// Password represents a password in the system
type Password struct {
	ID        uint64    `json:"id" db:"id"`
	Pwd       string    `json:"pwd" db:"pwd"`
	AccID     uint64    `json:"acc_id" db:"acc_id"`
	UsrID     uint64    `json:"usr_id" db:"usr_id"`
	CreatedAt time.Time `json:"created_at" db:"created_at"`
	UpdatedAt time.Time `json:"updated_at" db:"updated_at"`
}

// PwdValidateRequest represents the request payload for password validation
type PwdValidateRequest struct {
	UsrID    uint64 `json:"usr_id" binding:"required"`
	Password string `json:"password" binding:"required"`
}

type Token struct {
	Access  string `json:"access,omitempty"`
	Refresh string `json:"refresh,omitempty"`
}

type rToken struct {
	RToken string `json:"refresh"`
}

type RTokenRenewReq struct {
	rToken
}

type RTokenRenewRes struct {
	Token
}

type RTokenRevokeReq struct {
	rToken
}

type RTokenFetchAccUsrReq struct {
	rToken
}

type RTokenFetchAccUsrRes struct {
	UsrID uint64 `json:"usr_id"`
	AccID uint64 `json:"acc_id"`
}

type RefreshTokenBelongsToReq struct {
	rToken
	UsrID uint64 `json:"usr_id"`
	AccID uint64 `json:"acc_id"`
}

// PwdStrengthRequest represents the request payload for password strength check
type PwdStrengthRequest struct {
	Password string `json:"password" binding:"required"`
}

// PwdStrengthResponse represents the response for password strength check
type PwdStrengthResponse struct {
	Strong   bool     `json:"strong"`
	Messages []string `json:"messages,omitempty"`
}
