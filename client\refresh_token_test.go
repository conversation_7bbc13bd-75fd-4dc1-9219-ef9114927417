package client

import (
	"context"
	"net/http"
	"testing"

	"github.com/homewizeAI/authms/model"
	"github.com/jarcoal/httpmock"
	"github.com/stretchr/testify/assert"
)

func TestRefreshTokenOperations(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	// Mock Password Validate
	httpmock.RegisterResponder(
		"POST",
		"http://localhost:8080/auth/pwd/validate",
		func(req *http.Request) (*http.Response, error) {
			resp := model.Token{
				Access:  "test-access-token",
				Refresh: "test-refresh-token",
			}
			return httpmock.NewJsonResponse(http.StatusOK, resp)
		},
	)

	// Mock Belongs To
	httpmock.RegisterResponder(
		"POST",
		"http://localhost:8080/auth/token/belongs-to",
		func(req *http.Request) (*http.Response, error) {
			return httpmock.NewJsonResponse(http.StatusOK, true)
		},
	)

	// Mock Renew Token
	httpmock.RegisterResponder(
		"POST",
		"http://localhost:8080/auth/token/refresh",
		func(req *http.Request) (*http.Response, error) {
			resp := model.RTokenRenewRes{
				Token: model.Token{
					Access:  "new-access-token",
					Refresh: "new-refresh-token",
				},
			}
			return httpmock.NewJsonResponse(http.StatusOK, resp)
		},
	)

	// Mock Revoke Token
	httpmock.RegisterResponder(
		"DELETE",
		"http://localhost:8080/auth/token/revoke",
		func(req *http.Request) (*http.Response, error) {
			return httpmock.NewJsonResponse(http.StatusOK, uint64(1))
		},
	)

	// Mock Delete by User
	httpmock.RegisterResponder(
		"DELETE",
		"http://localhost:8080/auth/token/delete-by-user/123",
		func(req *http.Request) (*http.Response, error) {
			return httpmock.NewJsonResponse(http.StatusOK, uint64(1))
		},
	)

	ctx := context.Background()
	client := NewWithURLPfx("http://localhost:8080")

	// First get a refresh token through password validation
	pwdValidateReq := model.PwdValidateRequest{
		UsrID:    123,
		Password: "testPassword123!",
	}
	token, err := client.PwdValidate(ctx, pwdValidateReq)
	assert.NoError(t, err)
	assert.NotEmpty(t, token.Access)
	assert.NotEmpty(t, token.Refresh)

	// Test Belongs To
	belongsToReq := model.RefreshTokenBelongsToReq{
		UsrID: 123,
		AccID: 456,
	}
	belongs, err := client.RefreshTokenBelongsTo(ctx, belongsToReq)
	assert.NoError(t, err)
	assert.True(t, belongs)

	// Test Renew Token
	renewReq := model.RefreshTokenRequest{
		RefreshToken: token.Refresh,
	}
	renewResp, err := client.RenewRefreshToken(ctx, renewReq)
	assert.NoError(t, err)
	assert.NotEmpty(t, renewResp.Access)
	assert.NotEmpty(t, renewResp.Refresh)

	// Test Revoke Token
	revokeReq := model.RTokenRevokeReq{}
	revokeReq.RToken = renewResp.Refresh
	rowsAffected, err := client.RevokeRefreshToken(ctx, revokeReq)
	assert.NoError(t, err)
	assert.Equal(t, uint64(1), rowsAffected)

	// Test Delete by User
	rowsAffected, err = client.DeleteRefreshTokenByUser(ctx, 123)
	assert.NoError(t, err)
	assert.NotZero(t, rowsAffected)

	// Verify token no longer belongs to user
	belongs, err = client.RefreshTokenBelongsTo(ctx, belongsToReq)
	assert.NoError(t, err)
	assert.True(t, belongs) // Note: This is mocked to always return true
}
