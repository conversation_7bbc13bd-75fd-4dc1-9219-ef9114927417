#!/bin/bash

# Simple deployment script for Authentication Management Service

set -e

echo "🚀 Deploying Authentication Management Service..."

# Create configuration if it doesn't exist
if [ ! -f "conf/secrets.toml" ]; then
    echo "📝 Creating configuration file..."
    mkdir -p conf
    cat > conf/secrets.toml << EOF
[database]
driver = "postgres"
dataSource = "postgres://authms_user:authms_pass@127.0.0.1:5432/authms?sslmode=disable"
EOF
    echo "⚠️  Please update conf/secrets.toml with your database settings"
fi

# Verify build
echo "🔨 Verifying build..."
./build.sh

# Start the application
echo "🌟 Starting Authentication Management Service..."
echo "📋 Service will be available at: http://localhost:3001"
echo "🔧 Press Ctrl+C to stop"
echo ""

go run main.go
