package model

import "time"

// RefreshToken represents a refresh token record in the database
type RefreshToken struct {
	ID        uint64    `json:"id" db:"id"`
	Token     string    `json:"token" db:"token"`
	UserID    uint64    `json:"user_id" db:"user_id"`
	AccID     uint64    `json:"acc_id" db:"acc_id"`
	ValidFrom time.Time `json:"valid_from" db:"valid_from"`
}

// RefreshTokenRequest represents a request to refresh JWT token using refresh token
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

// RefreshTokenResponse represents the response containing new JWT and refresh tokens
type RefreshTokenResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresIn    int    `json:"expires_in"` // JWT expiration in seconds
}

// IsValid checks if the refresh token is valid (within valid_from time)
func (rt *RefreshToken) IsValid() bool {
	return time.Now().After(rt.ValidFrom)
}


