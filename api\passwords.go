package api

import (
	"regexp"
	"strings"

	"github.com/gin-gonic/gin"

	"github.com/homewizeAI/apicommon"
	"github.com/homewizeAI/apitypes"
	"github.com/homewizeAI/authms/dao"
	"github.com/homewizeAI/authms/model"
	"github.com/homewizeAI/errs"
	"github.com/homewizeAI/keylib"
	"github.com/homewizeAI/utils"
)

func PwdCreate(c *gin.Context) {
	var err error
	var data model.Password
	if err = c.BindJSON(&data); err != nil {
		apicommon.RespBadRequest(c, "payload")
		return
	}

	// Validate password strength
	strong, messages := validatePasswordStrength(data.Pwd)
	if !strong {
		c.JSON(400, gin.H{"error": "weak-password", "messages": messages})
		return
	}

	_, err = dao.PwdGetByUsrID(data.UsrID)
	if err != nil {
		if !errs.IsNotFound(err) {
			apicommon.RespISE(c, "password-get-error")
		}
	} else {
		apicommon.RespConflict(c, "already-set")
	}

	// Generate password hash using keylib
	hashedPassword, err := keylib.GenerateHashWithDefaults(data.Pwd)
	if err != nil {
		apicommon.RespISE(c, "password-hash-error")
		return
	}

	// Replace plain password with hash
	data.Pwd = hashedPassword

	id, err := dao.PwdCreate(data)
	if err != nil {
		if errs.IsBadInput(err) {
			apicommon.RespBadRequest(c, errs.GetReason(err))
			return
		}
		if errs.IsConflict(err) {
			apicommon.RespConflict(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, err.Error())
		return
	}
	apicommon.RespOk(c, apitypes.IDTypeNew(id))
}

func PwdUpdate(c *gin.Context) {
	idStr := c.Param("id")
	id, err := utils.AsUint64(idStr)
	if err != nil {
		apicommon.RespBadRequest(c, "id")
		return
	}

	var data model.Password
	if err = c.BindJSON(&data); err != nil {
		apicommon.RespBadRequest(c, "payload")
		return
	}

	// Validate password strength
	strong, messages := validatePasswordStrength(data.Pwd)
	if !strong {
		c.JSON(400, gin.H{"error": "weak-password", "messages": messages})
		return
	}

	// Generate password hash using keylib
	hashedPassword, err := keylib.GenerateHashWithDefaults(data.Pwd)
	if err != nil {
		apicommon.RespISE(c, "password-hash-error")
		return
	}

	// Replace plain password with hash
	data.Pwd = hashedPassword

	rowsAffected, err := dao.PwdUpdate(id, data)
	if err != nil {
		if errs.IsBadInput(err) {
			apicommon.RespBadRequest(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, "internal-error")
		return
	}

	apicommon.RespOk(c, apitypes.AffectedRowsTypeNew(rowsAffected))
}

func PwdDelete(c *gin.Context) {
	idStr := c.Param("id")
	id, err := utils.AsUint64(idStr)
	if err != nil {
		apicommon.RespBadRequest(c, "id")
		return
	}

	rowsAffected, err := dao.PwdDelete(id)
	if err != nil {
		if errs.IsBadInput(err) {
			apicommon.RespBadRequest(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, "internal-error")
		return
	}

	apicommon.RespOk(c, apitypes.AffectedRowsTypeNew(rowsAffected))
}

func PwdValidate(c *gin.Context) {
	var req model.PwdValidateRequest
	if err := c.BindJSON(&req); err != nil {
		apicommon.RespBadRequest(c, "payload")
		return
	}

	// Get stored password hash by user ID
	pwdRow, err := dao.PwdGetByUsrID(req.UsrID)
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, "user-password")
			return
		}
		apicommon.RespISE(c, "internal-error")
		return
	}

	// Compare password with stored hash using keylib
	isValid, err := keylib.ComparePasswordHash(req.Password, pwdRow.Pwd)
	if err != nil {
		apicommon.RespUnauthorized(c, "user/pwd")
		return
	}

	token := model.Token{}
	// If password is valid, generate JWT token and refresh token
	if isValid {
		token, err = getPwdValidateRTokenRes(pwdRow.UsrID, pwdRow.AccID)
		if err != nil {
			apicommon.RespISE(c, "pwd-token-regeneration")
			return
		}
	}

	apicommon.RespOk(c, token)
}

// validatePasswordStrength checks if a password meets strength requirements
func validatePasswordStrength(password string) (bool, []string) {
	var messages []string

	// Check minimum length
	if len(password) < 8 {
		messages = append(messages, "Password must be at least 8 characters long")
	}

	// Check for uppercase letter
	if matched, _ := regexp.MatchString(`[A-Z]`, password); !matched {
		messages = append(messages, "Password must contain at least one uppercase letter")
	}

	// Check for lowercase letter
	if matched, _ := regexp.MatchString(`[a-z]`, password); !matched {
		messages = append(messages, "Password must contain at least one lowercase letter")
	}

	// Check for digit
	if matched, _ := regexp.MatchString(`[0-9]`, password); !matched {
		messages = append(messages, "Password must contain at least one digit")
	}

	// Check for special character
	if matched, _ := regexp.MatchString(`[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]`, password); !matched {
		messages = append(messages, "Password must contain at least one special character")
	}

	// Check for common weak patterns
	lowerPassword := strings.ToLower(password)
	weakPatterns := []string{"password", "123456", "qwerty", "abc123", "admin", "user"}
	for _, pattern := range weakPatterns {
		if strings.Contains(lowerPassword, pattern) {
			messages = append(messages, "Password contains common weak patterns")
			break
		}
	}

	return len(messages) == 0, messages
}

// PwdCheckStrength checks the strength of a password
func PwdCheckStrength(c *gin.Context) {
	var req model.PwdStrengthRequest
	if err := c.BindJSON(&req); err != nil {
		apicommon.RespBadRequest(c, "payload")
		return
	}

	strong, messages := validatePasswordStrength(req.Password)
	response := model.PwdStrengthResponse{
		Strong:   strong,
		Messages: messages,
	}

	apicommon.RespOk(c, response)
}
