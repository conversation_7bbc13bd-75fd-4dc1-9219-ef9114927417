package tests

import (
	"fmt"
	"net/http"
	"testing"
	"time"
)

func TestRefreshTokenFlow(t *testing.T) {
	defer cleanupTestData()

	e := getExpect(t)

	// First create a password for the user
	password := createTestPassword(TestUserID, TestAccountID)
	resp := e.POST("/auth/pwd").
		WithJSON(password).
		Expect().
		Status(http.StatusOK).
		JSON()

	passwordID := uint64(resp.Object().Value("id").Number().Raw())
	testData.PasswordIDs = append(testData.PasswordIDs, passwordID)

	// Get initial tokens through password validation
	validateReq := createTestPwdValidateRequest(TestUserID, TestPassword)
	resp = e.POST("/auth/pwd/validate").
		WithJSON(validateReq).
		Expect().
		Status(http.StatusOK).
		JSON()

	// Extract tokens
	accessToken := resp.Object().Value("access").String().Raw()
	refreshToken := resp.Object().Value("refresh").String().Raw()

	if accessToken == "" || refreshToken == "" {
		t.Fatal("Initial tokens should not be empty")
	}

	// Test refresh token belongs to user
	belongsToReq := createTestRefreshTokenBelongsToRequest(TestUserID, TestAccountID, refreshToken)
	resp = e.POST("/auth/token/belongs-to").
		WithJSON(belongsToReq).
		Expect().
		Status(http.StatusOK).
		JSON()

	resp.Object().ContainsKey("bool")
	resp.Object().Value("bool").Boolean().IsTrue()

	// Test refresh token fetch acc usr
	fetchReq := createTestRTokenFetchAccUsrRequest(refreshToken)
	resp = e.POST("/auth/token/refresh/fetch-acc-usr").
		WithJSON(fetchReq).
		Expect().
		Status(http.StatusOK).
		JSON()

	resp.Object().ContainsKey("usr_id")
	resp.Object().ContainsKey("acc_id")
	resp.Object().Value("usr_id").Number().IsEqual(TestUserID)
	resp.Object().Value("acc_id").Number().IsEqual(TestAccountID)

	// Test token refresh
	// Add delay to ensure different JWT timestamps (JWT uses second precision)
	time.Sleep(time.Second * 1)
	refreshReq := createTestRefreshTokenRequest(refreshToken)
	resp = e.POST("/auth/token/refresh").
		WithJSON(refreshReq).
		Expect().
		Status(http.StatusOK).
		JSON()

	// Verify new tokens are returned
	resp.Object().ContainsKey("access")
	resp.Object().ContainsKey("refresh")
	newAccessToken := resp.Object().Value("access").String().Raw()
	newRefreshToken := resp.Object().Value("refresh").String().Raw()

	if newAccessToken == "" || newRefreshToken == "" {
		t.Fatal("New tokens should not be empty")
	}

	// Verify tokens are different from original
	if newAccessToken == accessToken {
		t.Errorf("New access token should be different from original\nOriginal: %s\nNew: %s", accessToken, newAccessToken)
	}
	if newRefreshToken == refreshToken {
		t.Error("New refresh token should be different from original")
	}

	// Test revoke refresh token
	revokeReq := createTestRTokenRevokeRequest(newRefreshToken)
	resp = e.DELETE("/auth/token/refresh").
		WithJSON(revokeReq).
		Expect().
		Status(http.StatusOK).
		JSON()

	resp.Object().ContainsKey("affected_rows")
	resp.Object().Value("affected_rows").Number().IsEqual(1)
}

func TestRefreshTokenBelongsTo(t *testing.T) {
	defer cleanupTestData()

	e := getExpect(t)

	// First create a password for the user
	password := createTestPassword(TestUserID, TestAccountID)
	resp := e.POST("/auth/pwd").
		WithJSON(password).
		Expect().
		Status(http.StatusOK).
		JSON()

	passwordID := uint64(resp.Object().Value("id").Number().Raw())
	testData.PasswordIDs = append(testData.PasswordIDs, passwordID)

	// Get tokens through password validation
	validateReq := createTestPwdValidateRequest(TestUserID, TestPassword)
	resp = e.POST("/auth/pwd/validate").
		WithJSON(validateReq).
		Expect().
		Status(http.StatusOK).
		JSON()

	refreshToken := resp.Object().Value("refresh").String().Raw()

	// Test token belongs to correct user and account
	belongsToReq := createTestRefreshTokenBelongsToRequest(TestUserID, TestAccountID, refreshToken)
	resp = e.POST("/auth/token/belongs-to").
		WithJSON(belongsToReq).
		Expect().
		Status(http.StatusOK).
		JSON()

	resp.Object().ContainsKey("bool")
	resp.Object().Value("bool").Boolean().IsTrue()

	// Test token does not belong to different user
	wrongUserReq := createTestRefreshTokenBelongsToRequest(**********, TestAccountID, refreshToken)
	resp = e.POST("/auth/token/belongs-to").
		WithJSON(wrongUserReq).
		Expect().
		Status(http.StatusOK).
		JSON()

	resp.Object().ContainsKey("bool")
	resp.Object().Value("bool").Boolean().IsFalse()

	// Test token does not belong to different account
	wrongAccReq := createTestRefreshTokenBelongsToRequest(TestUserID, **********, refreshToken)
	resp = e.POST("/auth/token/belongs-to").
		WithJSON(wrongAccReq).
		Expect().
		Status(http.StatusOK).
		JSON()

	resp.Object().ContainsKey("bool")
	resp.Object().Value("bool").Boolean().IsFalse()

	// Test with invalid token
	invalidTokenReq := createTestRefreshTokenBelongsToRequest(TestUserID, TestAccountID, "invalid-token")
	resp = e.POST("/auth/token/belongs-to").
		WithJSON(invalidTokenReq).
		Expect().
		Status(http.StatusOK).
		JSON()

	resp.Object().ContainsKey("bool")
	resp.Object().Value("bool").Boolean().IsFalse()
}

func TestRTokenDelByUser(t *testing.T) {
	defer cleanupTestData()

	e := getExpect(t)

	// First create a password for the user
	password := createTestPassword(TestUserID, TestAccountID)
	resp := e.POST("/auth/pwd").
		WithJSON(password).
		Expect().
		Status(http.StatusOK).
		JSON()

	passwordID := uint64(resp.Object().Value("id").Number().Raw())
	testData.PasswordIDs = append(testData.PasswordIDs, passwordID)

	// Get tokens through password validation (creates refresh token)
	validateReq := createTestPwdValidateRequest(TestUserID, TestPassword)
	e.POST("/auth/pwd/validate").
		WithJSON(validateReq).
		Expect().
		Status(http.StatusOK)

	// Delete all refresh tokens for user
	resp = e.DELETE(fmt.Sprintf("/auth/token/refresh/user/%d", TestUserID)).
		Expect().
		Status(http.StatusOK).
		JSON()

	resp.Object().ContainsKey("affected_rows")
	// Should delete at least 1 token
	resp.Object().Value("affected_rows").Number().Ge(1)

	// Test delete for non-existent user (should return 0 affected rows)
	resp = e.DELETE("/auth/token/refresh/user/999999").
		Expect().
		Status(http.StatusOK).
		JSON()

	resp.Object().ContainsKey("affected_rows")
	resp.Object().Value("affected_rows").Number().IsEqual(0)

	// Test with invalid user ID
	e.DELETE("/auth/token/refresh/user/invalid").
		Expect().
		Status(http.StatusBadRequest)
}

func TestRefreshTokenInvalidScenarios(t *testing.T) {
	defer cleanupTestData()

	e := getExpect(t)

	// Test refresh with invalid token
	invalidRefreshReq := createTestRefreshTokenRequest("invalid-token")
	e.POST("/auth/token/refresh").
		WithJSON(invalidRefreshReq).
		Expect().
		Status(http.StatusNotFound)

	// Test refresh with empty token
	emptyRefreshReq := createTestRefreshTokenRequest("")
	e.POST("/auth/token/refresh").
		WithJSON(emptyRefreshReq).
		Expect().
		Status(http.StatusBadRequest)

	// Test revoke with invalid token
	invalidRevokeReq := createTestRTokenRevokeRequest("invalid-token")
	resp := e.DELETE("/auth/token/refresh").
		WithJSON(invalidRevokeReq).
		Expect().
		Status(http.StatusOK).
		JSON()

	resp.Object().ContainsKey("affected_rows")
	resp.Object().Value("affected_rows").Number().IsEqual(0)

	// Test fetch acc usr with invalid token
	invalidFetchReq := createTestRTokenFetchAccUsrRequest("invalid-token")
	e.POST("/auth/token/refresh/fetch-acc-usr").
		WithJSON(invalidFetchReq).
		Expect().
		Status(http.StatusNotFound)
}

func TestRefreshTokenInvalidPayloads(t *testing.T) {
	defer cleanupTestData()

	e := getExpect(t)

	// Test refresh with invalid JSON
	e.POST("/auth/token/refresh").
		WithText("invalid json").
		Expect().
		Status(http.StatusBadRequest)

	// Test revoke with invalid JSON
	e.DELETE("/auth/token/refresh").
		WithText("invalid json").
		Expect().
		Status(http.StatusBadRequest)

	// Test belongs-to with invalid JSON
	e.POST("/auth/token/belongs-to").
		WithText("invalid json").
		Expect().
		Status(http.StatusBadRequest)

	// Test fetch-acc-usr with invalid JSON
	e.POST("/auth/token/refresh/fetch-acc-usr").
		WithText("invalid json").
		Expect().
		Status(http.StatusBadRequest)

	// Test refresh with missing refresh_token field
	e.POST("/auth/token/refresh").
		WithJSON(map[string]interface{}{}).
		Expect().
		Status(http.StatusBadRequest)
}

func TestRefreshTokenExpiredScenarios(t *testing.T) {
	defer cleanupTestData()

	e := getExpect(t)

	// First create a password for the user
	password := createTestPassword(TestUserID, TestAccountID)
	resp := e.POST("/auth/pwd").
		WithJSON(password).
		Expect().
		Status(http.StatusOK).
		JSON()

	passwordID := uint64(resp.Object().Value("id").Number().Raw())
	testData.PasswordIDs = append(testData.PasswordIDs, passwordID)

	// Get tokens through password validation
	validateReq := createTestPwdValidateRequest(TestUserID, TestPassword)
	resp = e.POST("/auth/pwd/validate").
		WithJSON(validateReq).
		Expect().
		Status(http.StatusOK).
		JSON()

	refreshToken := resp.Object().Value("refresh").String().Raw()

	// Use the refresh token to get new tokens
	refreshReq := createTestRefreshTokenRequest(refreshToken)
	resp = e.POST("/auth/token/refresh").
		WithJSON(refreshReq).
		Expect().
		Status(http.StatusOK).
		JSON()

	// Try to use the old refresh token again (should fail as it's been renewed)
	e.POST("/auth/token/refresh").
		WithJSON(refreshReq).
		Expect().
		Status(http.StatusNotFound)
}
