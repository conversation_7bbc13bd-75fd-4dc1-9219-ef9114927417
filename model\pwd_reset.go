package model

import (
	"time"
)

// PwdReset represents a password reset request in the system
type PwdReset struct {
	ID        uint64    `json:"id" db:"id"`
	ResetKey  string    `json:"reset_key" db:"reset_key"`
	PwdID     uint64    `json:"pwd_id" db:"pwd_id"`
	CreatedAt time.Time `json:"created_at" db:"created_at"`
	UpdatedAt time.Time `json:"updated_at" db:"updated_at"`
}

// PwdResetCreateRequest represents the request payload for creating a password reset
type PwdResetCreateRequest struct {
	UsrID uint64 `json:"usr_id" binding:"required"`
}

// PwdResetCreateResponse represents the response payload for creating a password reset
type PwdResetCreateResponse struct {
	ResetKey string `json:"reset_key"`
	ID       uint64 `json:"id"`
}

type PwdResetKeyGetRequest struct {
	ResetKey string `json:"reset_key"`
	ID       uint64 `json:"id"`
}

type PwdResetKeyGetResponse struct {
	AccID uint64 `json:"acc_id"`
	UsrID uint64 `json:"usr_id"`
}

type PwdResetUpdateRequest struct {
	ResetKey string `json:"reset_key"`
	ID       uint64 `json:"id"`
	Pwd      string `json:"pwd"`
}

type PwdResetDeleteRequest struct {
	ResetKey string `json:"reset_key"`
	ID       uint64 `json:"id"`
}

// PwdResetValidateRequest represents the request payload for validating a reset key
type PwdResetValidateRequest struct {
	ResetKey string `json:"reset_key" binding:"required"`
}

// PwdResetValidateResponse represents the response payload for validating a reset key
type PwdResetValidateResponse struct {
	Valid bool   `json:"valid"`
	PwdID uint64 `json:"pwd_id,omitempty"`
}
