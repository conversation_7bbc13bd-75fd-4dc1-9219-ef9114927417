package api

import (
	"github.com/gin-gonic/gin"
	"github.com/homewizeAI/apicommon"
	"github.com/homewizeAI/apitypes"
	"github.com/homewizeAI/authms/dao"
	"github.com/homewizeAI/authms/model"
	"github.com/homewizeAI/errs"
	"github.com/homewizeAI/utils"
)

// PwdResetCreate creates a new password reset request
func PwdResetCreate(c *gin.Context) {
	var req model.PwdResetCreateRequest
	if err := c.BindJSON(&req); err != nil {
		apicommon.RespBadRequest(c, "payload")
		return
	}

	res, err := dao.PwdResetCreate(req)
	if err != nil {
		if errs.IsBadInput(err) {
			apicommon.RespBadRequest(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, "internal-error")
		return
	}

	apicommon.RespOk(c, res)
}

// PwdResetValidate validates a reset key
func PwdResetValidate(c *gin.Context) {
	var req model.PwdResetValidateRequest
	if err := c.BindJSON(&req); err != nil {
		apicommon.RespBadRequest(c, "payload")
		return
	}

	// Get password reset by key
	reset, err := dao.PwdResetGetByKey(req.ResetKey)
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespOk(c, model.PwdResetValidateResponse{Valid: false})
			return
		}
		apicommon.RespISE(c, "internal-error")
		return
	}

	response := model.PwdResetValidateResponse{
		Valid: true,
		PwdID: reset.PwdID,
	}

	apicommon.RespOk(c, response)
}

// PwdResetDelete deletes a password reset request
func PwdResetDelete(c *gin.Context) {
	var req model.PwdResetDeleteRequest
	if err := c.BindJSON(&req); err != nil {
		apicommon.RespBadRequest(c, "payload")
		return
	}

	rowsAffected, err := dao.PwdResetDelete(req.ID, req.ResetKey)
	if err != nil {
		apicommon.RespISE(c, "internal-error")
		return
	}

	apicommon.RespOk(c, apitypes.AffectedRowsTypeNew(rowsAffected))
}

func PwdResetKeyGet(c *gin.Context) {
	var req model.PwdResetKeyGetRequest
	if err := c.BindJSON(&req); err != nil {
		apicommon.RespBadRequest(c, "payload")
		return
	}

	res, err := dao.PwdResetKeyGet(req)
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, "internal-error")
		return
	}

	apicommon.RespOk(c, res)
}

func PwdResetUpdate(c *gin.Context) {
	var req model.PwdResetUpdateRequest
	if err := c.BindJSON(&req); err != nil {
		apicommon.RespBadRequest(c, "payload")
		return
	}

	// Check password strength
	strong, messages := validatePasswordStrength(req.Pwd)
	if !strong {
		c.JSON(400, gin.H{"error": "weak-password", "messages": messages})
		return
	}

	rowsAffected, err := dao.PwdResetUpdate(req)
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, "internal-error")
		return
	}

	apicommon.RespOk(c, apitypes.AffectedRowsTypeNew(rowsAffected))
}

func PwdResetHasActiveKey(c *gin.Context) {
	usrID, err := utils.AsUint64(c.Param("usrID"))
	if err != nil {
		apicommon.RespBadRequest(c, "usrID")
		return
	}

	res, err := dao.PwdResetHasActiveKey(usrID)
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespOk(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, "internal-error")
		return
	}

	apicommon.RespOk(c, apitypes.BoolTypeNew(res))
}
