# Authentication Keys Configuration

[jwt]
signing_key = "LeaPWqvyHl00NaQ449_QoLHP8Sb8LoKxKuVAVShGxSWeiJOB9KXsMxfHCavyifVTEccmcDm71FB9eJ_-ZQiNaA=="

# JWT Token issuer
issuer = "authms"

# JWT Token expiration time in minutes (default: 15 minutes)
expiration_minutes = 15

# JWT Token refresh expiration time in hours (default: 168 hours = 7 days)
refresh_expiration_hours = 168

[refresh_token]
# Refresh token expiration time in hours (default: 168 hours = 7 days)
expiration_hours = 168
