package core

import (
	"fmt"
	"os"

	"github.com/BurntSushi/toml"
)

// AuthConfig represents the authentication configuration
type AuthConfig struct {
	JWT          JWTConfig          `toml:"jwt"`
	RefreshToken RefreshTokenConfig `toml:"refresh_token"`
}

// JWTConfig represents JWT-specific configuration
type JWTConfig struct {
	TokenSignKey           string `toml:"signing_key"`
	Issuer                 string `toml:"issuer"`
	ExpirationMinutes      int    `toml:"expiration_minutes"`
	RefreshExpirationHours int    `toml:"refresh_expiration_hours"`
}

// RefreshTokenConfig represents refresh token configuration
type RefreshTokenConfig struct {
	ExpirationHours int `toml:"expiration_hours"`
}

// LoadAuthConfig loads authentication configuration from authkeys.toml
func LoadAuthConfig(configPath string) (*AuthConfig, error) {
	// Check if config file exists
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("config file not found: %s", configPath)
	}

	var config AuthConfig

	// Parse TOML file
	if _, err := toml.DecodeFile(configPath, &config); err != nil {
		return nil, fmt.Errorf("failed to parse config file: %w", err)
	}

	// Validate required fields
	if config.JWT.TokenSignKey == "" {
		return nil, fmt.Errorf("token_sign_key is required in config")
	}

	// Set defaults if not specified
	if config.JWT.Issuer == "" {
		config.JWT.Issuer = "authms"
	}
	if config.JWT.ExpirationMinutes == 0 {
		config.JWT.ExpirationMinutes = 15
	}
	if config.JWT.RefreshExpirationHours == 0 {
		config.JWT.RefreshExpirationHours = 168
	}
	if config.RefreshToken.ExpirationHours == 0 {
		config.RefreshToken.ExpirationHours = 168 // 7 days default
	}

	return &config, nil
}

// GetJWTManager creates a JWT manager from the configuration
func (c *AuthConfig) GetJWTManager() *JWTManager {
	return NewJWTManager(c.JWT.TokenSignKey, c.JWT.Issuer)
}
