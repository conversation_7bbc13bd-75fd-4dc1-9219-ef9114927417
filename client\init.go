package client

import (
	"github.com/homewizeAI/clientcommon"
	_ "github.com/lib/pq"
)

const (
	cbKey = "authclient"
	k8sNS = "default"
	ms    = "authms"
)

type Client struct {
	clientcommon.Client
}

func NewK8sNS(ns string) *Client {
	o := &Client{}
	o.InitUrlPfxK8sNS(ms, ns)
	o.InitHttpClient(ms)
	return o
}

func NewK8s() *Client {
	return NewK8sNS(k8sNS)
}

func NewWithURLPfx(urlPfx string) *Client {
	o := &Client{}
	o.UrlPfx = urlPfx
	o.InitHttpClient(ms)
	return o
}
