-- Create pwds table
CREATE TABLE pwds (
    id BIGSERIAL PRIMARY KEY,
    pwd VARCHAR(255) NOT NULL,
    acc_id BIGINT NOT NULL,
    usr_id BIGINT NOT NULL UNIQUE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for performance
CREATE INDEX idx_pwds_acc_id ON pwds(acc_id);
CREATE INDEX idx_pwds_usr_id ON pwds(usr_id);
