package tests

import (
	"fmt"
	"net/http"
	"testing"
)

func TestPwdResetCreate(t *testing.T) {
	defer cleanupTestData()

	e := getExpect(t)

	// First create a password for the user
	password := createTestPassword(TestUserID, TestAccountID)
	resp := e.POST("/auth/pwd").
		WithJSON(password).
		Expect().
		Status(http.StatusOK).
		JSON()

	passwordID := uint64(resp.Object().Value("id").Number().Raw())
	testData.PasswordIDs = append(testData.PasswordIDs, passwordID)

	// Test successful password reset creation
	resetReq := createTestPwdResetRequest(TestUserID)
	resp = e.POST("/auth/pwd/reset").
		WithJSON(resetReq).
		Expect().
		Status(http.StatusOK).
		JSON()

	// Verify response structure
	resp.Object().ContainsKey("id")
	resp.Object().ContainsKey("reset_key")
	resetID := uint64(resp.Object().Value("id").Number().Raw())
	resetKey := resp.Object().Value("reset_key").String().Raw()
	testData.PwdResetIDs = append(testData.PwdResetIDs, resetID)

	// Verify reset key is not empty
	if resetKey == "" {
		t.Error("Reset key should not be empty")
	}

	// Test creating reset for non-existent user
	nonExistentReq := createTestPwdResetRequest(999999)
	e.POST("/auth/pwd/reset").
		WithJSON(nonExistentReq).
		Expect().
		Status(http.StatusBadRequest)
}

func TestPwdResetKeyGet(t *testing.T) {
	defer cleanupTestData()

	e := getExpect(t)

	// First create a password for the user
	password := createTestPassword(TestUserID, TestAccountID)
	resp := e.POST("/auth/pwd").
		WithJSON(password).
		Expect().
		Status(http.StatusOK).
		JSON()

	passwordID := uint64(resp.Object().Value("id").Number().Raw())
	testData.PasswordIDs = append(testData.PasswordIDs, passwordID)

	// Create password reset
	resetReq := createTestPwdResetRequest(TestUserID)
	resp = e.POST("/auth/pwd/reset").
		WithJSON(resetReq).
		Expect().
		Status(http.StatusOK).
		JSON()

	resetID := uint64(resp.Object().Value("id").Number().Raw())
	resetKey := resp.Object().Value("reset_key").String().Raw()
	testData.PwdResetIDs = append(testData.PwdResetIDs, resetID)

	// Test successful key get
	keyGetReq := createTestPwdResetKeyGetRequest(resetKey, resetID)
	resp = e.POST("/auth/pwd/reset/key").
		WithJSON(keyGetReq).
		Expect().
		Status(http.StatusOK).
		JSON()

	// Verify response structure
	resp.Object().ContainsKey("acc_id")
	resp.Object().ContainsKey("usr_id")
	resp.Object().Value("usr_id").Number().IsEqual(TestUserID)
	resp.Object().Value("acc_id").Number().IsEqual(TestAccountID)

	// Test with invalid reset key
	invalidKeyReq := createTestPwdResetKeyGetRequest("invalid-key", resetID)
	e.POST("/auth/pwd/reset/key").
		WithJSON(invalidKeyReq).
		Expect().
		Status(http.StatusNotFound)

	// Test with invalid ID
	invalidIDReq := createTestPwdResetKeyGetRequest(resetKey, 999999)
	e.POST("/auth/pwd/reset/key").
		WithJSON(invalidIDReq).
		Expect().
		Status(http.StatusNotFound)
}

func TestPwdResetUpdate(t *testing.T) {
	defer cleanupTestData()

	e := getExpect(t)

	// First create a password for the user
	password := createTestPassword(TestUserID, TestAccountID)
	resp := e.POST("/auth/pwd").
		WithJSON(password).
		Expect().
		Status(http.StatusOK).
		JSON()

	passwordID := uint64(resp.Object().Value("id").Number().Raw())
	testData.PasswordIDs = append(testData.PasswordIDs, passwordID)

	// Create password reset
	resetReq := createTestPwdResetRequest(TestUserID)
	resp = e.POST("/auth/pwd/reset").
		WithJSON(resetReq).
		Expect().
		Status(http.StatusOK).
		JSON()

	resetID := uint64(resp.Object().Value("id").Number().Raw())
	resetKey := resp.Object().Value("reset_key").String().Raw()
	testData.PwdResetIDs = append(testData.PwdResetIDs, resetID)

	// Test successful password update
	newPassword := "NewTestPassword123!"
	updateReq := createTestPwdResetUpdateRequest(resetKey, resetID, newPassword)
	e.PUT("/auth/pwd/reset/update").
		WithJSON(updateReq).
		Expect().
		Status(http.StatusOK).
		JSON().Object().ContainsKey("affected_rows")

	// Test with invalid reset key
	invalidUpdateReq := createTestPwdResetUpdateRequest("invalid-key", resetID, newPassword)
	e.PUT("/auth/pwd/reset/update").
		WithJSON(invalidUpdateReq).
		Expect().
		Status(http.StatusNotFound)

	// Test with invalid ID
	invalidIDUpdateReq := createTestPwdResetUpdateRequest(resetKey, 999999, newPassword)
	e.PUT("/auth/pwd/reset/update").
		WithJSON(invalidIDUpdateReq).
		Expect().
		Status(http.StatusNotFound)
}

func TestPwdResetDelete(t *testing.T) {
	defer cleanupTestData()

	e := getExpect(t)

	// First create a password for the user
	password := createTestPassword(TestUserID, TestAccountID)
	resp := e.POST("/auth/pwd").
		WithJSON(password).
		Expect().
		Status(http.StatusOK).
		JSON()

	passwordID := uint64(resp.Object().Value("id").Number().Raw())
	testData.PasswordIDs = append(testData.PasswordIDs, passwordID)

	// Create password reset
	resetReq := createTestPwdResetRequest(TestUserID)
	resp = e.POST("/auth/pwd/reset").
		WithJSON(resetReq).
		Expect().
		Status(http.StatusOK).
		JSON()

	resetID := uint64(resp.Object().Value("id").Number().Raw())
	resetKey := resp.Object().Value("reset_key").String().Raw()

	// Test successful password reset deletion
	deleteReq := createTestPwdResetDeleteRequest(resetKey, resetID)
	e.DELETE("/auth/pwd/reset").
		WithJSON(deleteReq).
		Expect().
		Status(http.StatusOK).
		JSON().Object().ContainsKey("affected_rows")

	// Test delete non-existent reset
	e.DELETE("/auth/pwd/reset").
		WithJSON(deleteReq).
		Expect().
		Status(http.StatusNotFound)
}

func TestPwdResetHasActiveKey(t *testing.T) {
	defer cleanupTestData()

	e := getExpect(t)

	// First create a password for the user
	password := createTestPassword(TestUserID, TestAccountID)
	resp := e.POST("/auth/pwd").
		WithJSON(password).
		Expect().
		Status(http.StatusOK).
		JSON()

	passwordID := uint64(resp.Object().Value("id").Number().Raw())
	testData.PasswordIDs = append(testData.PasswordIDs, passwordID)

	// Test user without active reset key
	resp = e.GET(fmt.Sprintf("/auth/pwd/reset/has-active-key/%d", TestUserID)).
		Expect().
		Status(http.StatusOK).
		JSON()

	resp.Object().ContainsKey("value")
	resp.Object().Value("value").Boolean().IsFalse()

	// Create password reset
	resetReq := createTestPwdResetRequest(TestUserID)
	resp = e.POST("/auth/pwd/reset").
		WithJSON(resetReq).
		Expect().
		Status(http.StatusOK).
		JSON()

	resetID := uint64(resp.Object().Value("id").Number().Raw())
	testData.PwdResetIDs = append(testData.PwdResetIDs, resetID)

	// Test user with active reset key
	resp = e.GET(fmt.Sprintf("/auth/pwd/reset/has-active-key/%d", TestUserID)).
		Expect().
		Status(http.StatusOK).
		JSON()

	resp.Object().ContainsKey("value")
	resp.Object().Value("value").Boolean().IsTrue()

	// Test with invalid user ID
	e.GET("/auth/pwd/reset/has-active-key/invalid").
		Expect().
		Status(http.StatusBadRequest)
}

func TestPwdResetValidate(t *testing.T) {
	defer cleanupTestData()

	e := getExpect(t)

	// First create a password for the user
	password := createTestPassword(TestUserID, TestAccountID)
	resp := e.POST("/auth/pwd").
		WithJSON(password).
		Expect().
		Status(http.StatusOK).
		JSON()

	passwordID := uint64(resp.Object().Value("id").Number().Raw())
	testData.PasswordIDs = append(testData.PasswordIDs, passwordID)

	// Create password reset
	resetReq := createTestPwdResetRequest(TestUserID)
	resp = e.POST("/auth/pwd/reset").
		WithJSON(resetReq).
		Expect().
		Status(http.StatusOK).
		JSON()

	resetID := uint64(resp.Object().Value("id").Number().Raw())
	resetKey := resp.Object().Value("reset_key").String().Raw()
	testData.PwdResetIDs = append(testData.PwdResetIDs, resetID)

	// Test successful reset key validation
	validateReq := createTestPwdResetValidateRequest(resetKey)
	resp = e.POST("/auth/pwd/reset/validate").
		WithJSON(validateReq).
		Expect().
		Status(http.StatusOK).
		JSON()

	// Verify response structure
	resp.Object().ContainsKey("valid")
	resp.Object().Value("valid").Boolean().IsTrue()
	resp.Object().ContainsKey("pwd_id")

	// Test with invalid reset key
	invalidValidateReq := createTestPwdResetValidateRequest("invalid-key")
	resp = e.POST("/auth/pwd/reset/validate").
		WithJSON(invalidValidateReq).
		Expect().
		Status(http.StatusOK).
		JSON()

	resp.Object().ContainsKey("valid")
	resp.Object().Value("valid").Boolean().IsFalse()
}

func TestPwdResetInvalidPayloads(t *testing.T) {
	defer cleanupTestData()

	e := getExpect(t)

	// Test create with invalid JSON
	e.POST("/auth/pwd/reset").
		WithText("invalid json").
		Expect().
		Status(http.StatusBadRequest)

	// Test create with missing usr_id
	e.POST("/auth/pwd/reset").
		WithJSON(map[string]interface{}{}).
		Expect().
		Status(http.StatusBadRequest)

	// Test key get with invalid JSON
	e.POST("/auth/pwd/reset/key").
		WithText("invalid json").
		Expect().
		Status(http.StatusBadRequest)

	// Test update with invalid JSON
	e.PUT("/auth/pwd/reset/update").
		WithText("invalid json").
		Expect().
		Status(http.StatusBadRequest)

	// Test delete with invalid JSON
	e.DELETE("/auth/pwd/reset").
		WithText("invalid json").
		Expect().
		Status(http.StatusBadRequest)

	// Test validate with invalid JSON
	e.POST("/auth/pwd/reset/validate").
		WithText("invalid json").
		Expect().
		Status(http.StatusBadRequest)

	// Test validate with missing reset_key
	e.POST("/auth/pwd/reset/validate").
		WithJSON(map[string]interface{}{}).
		Expect().
		Status(http.StatusBadRequest)
}
