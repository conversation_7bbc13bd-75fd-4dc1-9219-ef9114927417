-- Create pwd_resets table
CREATE TABLE pwd_resets (
    id BIGSERIAL PRIMARY KEY,
    reset_key VARCHAR(255) NOT NULL,
    pwd_id BIGINT NOT NULL REFERENCES pwds(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for performance
CREATE INDEX idx_pwd_resets_pwd_id ON pwd_resets(pwd_id);
CREATE INDEX idx_pwd_resets_reset_key ON pwd_resets(reset_key); 