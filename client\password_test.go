package client

import (
	"context"
	"encoding/json"
	"net/http"
	"testing"

	"github.com/homewizeAI/apitypes"
	"github.com/homewizeAI/authms/model"
	"github.com/jarcoal/httpmock"
)

func TestPwdCreate(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"POST",
		"http://localhost:8080/auth/pwd",
		func(req *http.Request) (*http.Response, error) {
			var pwd model.Password
			err := json.NewDecoder(req.Body).Decode(&pwd)
			if err != nil {
				t.Error(err)
				out := map[string]string{"error": "bad-request"}
				return httpmock.NewJsonResponse(http.StatusInternalServerError, out)
			}
			var resp apitypes.IDType
			resp.ID = 1
			return httpmock.NewJsonResponse(http.StatusOK, resp)
		},
	)

	var pwd model.Password
	pwd.Pwd = "SecurePassword123!"
	pwd.AccID = 1
	pwd.UsrID = 1
	id, err := c.PwdCreate(context.Background(), pwd)
	if err != nil {
		t.Error(err)
	}
	if id != 1 {
		t.Errorf("Expected id 1, got %d", id)
	}
}

func TestPwdCreateWeakPassword(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"POST",
		"http://localhost:8080/auth/pwd",
		func(req *http.Request) (*http.Response, error) {
			var pwd model.Password
			err := json.NewDecoder(req.Body).Decode(&pwd)
			if err != nil {
				t.Error(err)
				out := map[string]string{"error": "bad-request"}
				return httpmock.NewJsonResponse(http.StatusInternalServerError, out)
			}
			// Simulate weak password response
			out := map[string]interface{}{
				"error": "weak-password",
				"messages": []string{
					"Password must be at least 8 characters long",
					"Password must contain at least one uppercase letter",
				},
			}
			return httpmock.NewJsonResponse(http.StatusBadRequest, out)
		},
	)

	var pwd model.Password
	pwd.Pwd = "weak"
	pwd.AccID = 1
	pwd.UsrID = 1
	_, err := c.PwdCreate(context.Background(), pwd)
	if err == nil {
		t.Error("Expected error for weak password, got nil")
	}
}

func TestPwdUpdate(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"PUT",
		"http://localhost:8080/auth/pwd/1",
		func(req *http.Request) (*http.Response, error) {
			var pwd model.Password
			err := json.NewDecoder(req.Body).Decode(&pwd)
			if err != nil {
				t.Error(err)
				out := map[string]string{"error": "bad-request"}
				return httpmock.NewJsonResponse(http.StatusInternalServerError, out)
			}
			var resp apitypes.AffectedRowsType
			resp.AffectedRows = 1
			return httpmock.NewJsonResponse(http.StatusOK, resp)
		},
	)

	var pwd model.Password
	pwd.Pwd = "NewSecurePassword123!"
	pwd.AccID = 1
	pwd.UsrID = 1
	rowsAffected, err := c.PwdUpdate(context.Background(), 1, pwd)
	if err != nil {
		t.Error(err)
	}
	if rowsAffected != 1 {
		t.Errorf("Expected 1 row affected, got %d", rowsAffected)
	}
}

func TestPwdDelete(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"DELETE",
		"http://localhost:8080/auth/pwd/1",
		func(req *http.Request) (*http.Response, error) {
			var resp apitypes.AffectedRowsType
			resp.AffectedRows = 1
			return httpmock.NewJsonResponse(http.StatusOK, resp)
		},
	)

	rowsAffected, err := c.PwdDelete(context.Background(), 1)
	if err != nil {
		t.Error(err)
	}
	if rowsAffected != 1 {
		t.Errorf("Expected 1 row affected, got %d", rowsAffected)
	}
}

func TestPwdValidate(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"POST",
		"http://localhost:8080/auth/pwd/validate",
		func(req *http.Request) (*http.Response, error) {
			var validateReq model.PwdValidateRequest
			err := json.NewDecoder(req.Body).Decode(&validateReq)
			if err != nil {
				t.Error(err)
				out := map[string]string{"error": "bad-request"}
				return httpmock.NewJsonResponse(http.StatusInternalServerError, out)
			}
			resp := model.Token{
				Access:  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************.test-token",
				Refresh: "test-refresh-token-64-chars-long-secure-random-string-here",
			}
			return httpmock.NewJsonResponse(http.StatusOK, resp)
		},
	)

	validateReq := model.PwdValidateRequest{
		UsrID:    1,
		Password: "SecurePassword123!",
	}
	result, err := c.PwdValidate(context.Background(), validateReq)
	if err != nil {
		t.Error(err)
	}

	if result.Access == "" {
		t.Error("Expected JWT token to be present in valid response")
	}
	if result.Refresh == "" {
		t.Error("Expected refresh token to be present in valid response")
	}
}

func TestPwdValidateInvalid(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"POST",
		"http://localhost:8080/auth/pwd/validate",
		func(req *http.Request) (*http.Response, error) {
			var validateReq model.PwdValidateRequest
			err := json.NewDecoder(req.Body).Decode(&validateReq)
			if err != nil {
				t.Error(err)
				out := map[string]string{"error": "bad-request"}
				return httpmock.NewJsonResponse(http.StatusInternalServerError, out)
			}
			resp := model.Token{}
			return httpmock.NewJsonResponse(http.StatusOK, resp)
		},
	)

	validateReq := model.PwdValidateRequest{
		UsrID:    1,
		Password: "wrongpassword",
	}
	result, err := c.PwdValidate(context.Background(), validateReq)
	if err != nil {
		t.Error(err)
	}
	if result == nil {
		t.Error("Expected validation response, got nil")
	}
	if result.Access != "" {
		t.Error("Expected no access token for invalid password")
	}
	if result.Refresh != "" {
		t.Error("Expected no refresh token for invalid password")
	}
}

func TestPwdCheckStrengthStrong(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"POST",
		"http://localhost:8080/auth/pwd/check-strength",
		func(req *http.Request) (*http.Response, error) {
			var strengthReq model.PwdStrengthRequest
			err := json.NewDecoder(req.Body).Decode(&strengthReq)
			if err != nil {
				t.Error(err)
				out := map[string]string{"error": "bad-request"}
				return httpmock.NewJsonResponse(http.StatusInternalServerError, out)
			}
			resp := model.PwdStrengthResponse{
				Strong:   true,
				Messages: []string{},
			}
			return httpmock.NewJsonResponse(http.StatusOK, resp)
		},
	)

	strengthReq := model.PwdStrengthRequest{
		Password: "StrongPassword123!",
	}
	result, err := c.PwdCheckStrength(context.Background(), strengthReq)
	if err != nil {
		t.Error(err)
	}
	if !result.Strong {
		t.Error("Expected password to be strong")
	}
	if len(result.Messages) > 0 {
		t.Error("Expected no messages for strong password")
	}
}

func TestPwdCheckStrengthWeak(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"POST",
		"http://localhost:8080/auth/pwd/check-strength",
		func(req *http.Request) (*http.Response, error) {
			var strengthReq model.PwdStrengthRequest
			err := json.NewDecoder(req.Body).Decode(&strengthReq)
			if err != nil {
				t.Error(err)
				out := map[string]string{"error": "bad-request"}
				return httpmock.NewJsonResponse(http.StatusInternalServerError, out)
			}
			resp := model.PwdStrengthResponse{
				Strong: false,
				Messages: []string{
					"Password must be at least 8 characters long",
					"Password must contain at least one uppercase letter",
				},
			}
			return httpmock.NewJsonResponse(http.StatusOK, resp)
		},
	)

	strengthReq := model.PwdStrengthRequest{
		Password: "weak",
	}
	result, err := c.PwdCheckStrength(context.Background(), strengthReq)
	if err != nil {
		t.Error(err)
	}
	if result.Strong {
		t.Error("Expected password to be weak")
	}
	if len(result.Messages) == 0 {
		t.Error("Expected messages for weak password")
	}
}

func TestRefreshToken(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"POST",
		"http://localhost:8080/auth/token/refresh",
		func(req *http.Request) (*http.Response, error) {
			var refreshReq model.RefreshTokenRequest
			err := json.NewDecoder(req.Body).Decode(&refreshReq)
			if err != nil {
				t.Error(err)
				out := map[string]string{"error": "bad-request"}
				return httpmock.NewJsonResponse(http.StatusInternalServerError, out)
			}
			resp := model.RefreshTokenResponse{
				AccessToken:  "new-access-token",
				RefreshToken: "new-refresh-token",
				ExpiresIn:    3600,
			}
			return httpmock.NewJsonResponse(http.StatusOK, resp)
		},
	)

	refreshReq := model.RefreshTokenRequest{
		RefreshToken: "old-refresh-token",
	}
	result, err := c.RefreshToken(context.Background(), refreshReq)
	if err != nil {
		t.Error(err)
	}
	if result.AccessToken == "" {
		t.Error("Expected new access token")
	}
	if result.RefreshToken == "" {
		t.Error("Expected new refresh token")
	}
	if result.ExpiresIn != 3600 {
		t.Error("Expected expiration time to be 3600 seconds")
	}
}
