package main

import (
	"database/sql"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"strings"

	_ "github.com/lib/pq"
)

const (
	dbHost     = "localhost"
	dbPort     = 5432
	dbUser     = "postgres"
	dbPassword = "postgres"
	dbName     = "authms"
)

type Migration struct {
	Version int
	File    string
}

var upMigrations = []Migration{
	{1, "db/migrations/001_create_pwds_up.sql"},
	{2, "db/migrations/002_create_refresh_tokens_up.sql"},
	{3, "db/migrations/003_create_pwd_resets_up.sql"},
}

var downMigrations = []Migration{
	{3, "db/migrations/003_create_pwd_resets_down.sql"},
	{2, "db/migrations/002_create_refresh_tokens_down.sql"},
	{1, "db/migrations/001_create_pwds_down.sql"},
}

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: go run migrate.go [up|down]")
		os.Exit(1)
	}

	command := os.Args[1]
	if command != "up" && command != "down" {
		fmt.Println("Usage: go run migrate.go [up|down]")
		os.Exit(1)
	}

	// First, ensure the database exists
	err := ensureDatabaseExists()
	if err != nil {
		log.Fatal("Failed to ensure database exists:", err)
	}

	// Connect to target database
	psqlInfo := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=disable",
		dbHost, dbPort, dbUser, dbPassword, dbName)

	db, err := sql.Open("postgres", psqlInfo)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}
	defer db.Close()

	err = db.Ping()
	if err != nil {
		log.Fatal("Failed to ping database:", err)
	}

	fmt.Println("Connected to database successfully")

	// Create schema_migrations table if it doesn't exist
	createTableSQL := `
	CREATE TABLE IF NOT EXISTS schema_migrations (
		version BIGINT PRIMARY KEY,
		dirty BOOLEAN NOT NULL DEFAULT FALSE
	);`

	_, err = db.Exec(createTableSQL)
	if err != nil {
		log.Fatal("Failed to create schema_migrations table:", err)
	}

	if command == "up" {
		runUpMigrations(db)
	} else {
		runDownMigrations(db)
	}
}

func ensureDatabaseExists() error {
	// Connect to postgres database to check/create target database
	psqlInfo := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=postgres sslmode=disable",
		dbHost, dbPort, dbUser, dbPassword)

	db, err := sql.Open("postgres", psqlInfo)
	if err != nil {
		return fmt.Errorf("failed to connect to postgres database: %v", err)
	}
	defer db.Close()

	// Check if database exists
	var exists bool
	err = db.QueryRow("SELECT EXISTS(SELECT datname FROM pg_catalog.pg_database WHERE datname = $1)", dbName).Scan(&exists)
	if err != nil {
		return fmt.Errorf("failed to check if database exists: %v", err)
	}

	if !exists {
		fmt.Printf("Database %s does not exist, creating it...\n", dbName)
		_, err = db.Exec(fmt.Sprintf("CREATE DATABASE %s", dbName))
		if err != nil {
			return fmt.Errorf("failed to create database %s: %v", dbName, err)
		}
		fmt.Printf("Database %s created successfully\n", dbName)
	} else {
		fmt.Printf("Database %s already exists\n", dbName)
	}

	return nil
}

func isMigrationApplied(db *sql.DB, version int) bool {
	var count int
	err := db.QueryRow("SELECT COUNT(*) FROM schema_migrations WHERE version = $1", version).Scan(&count)
	if err != nil {
		log.Printf("Error checking migration status: %v", err)
		return false
	}
	return count > 0
}

func markMigrationApplied(db *sql.DB, version int) error {
	_, err := db.Exec("INSERT INTO schema_migrations (version, dirty) VALUES ($1, FALSE)", version)
	return err
}

func markMigrationUnapplied(db *sql.DB, version int) error {
	_, err := db.Exec("DELETE FROM schema_migrations WHERE version = $1", version)
	return err
}

func executeMigrationFile(db *sql.DB, filename string) error {
	content, err := ioutil.ReadFile(filename)
	if err != nil {
		return fmt.Errorf("failed to read file %s: %v", filename, err)
	}

	// Split by semicolon and execute each statement
	statements := strings.Split(string(content), ";")
	for _, stmt := range statements {
		stmt = strings.TrimSpace(stmt)
		if stmt == "" {
			continue
		}
		_, err = db.Exec(stmt)
		if err != nil {
			return fmt.Errorf("failed to execute statement: %v", err)
		}
	}
	return nil
}

func runUpMigrations(db *sql.DB) {
	fmt.Println("Starting UP migrations...")

	for _, migration := range upMigrations {
		if isMigrationApplied(db, migration.Version) {
			fmt.Printf("Migration %d already applied, skipping...\n", migration.Version)
			continue
		}

		fmt.Printf("Applying migration %d: %s\n", migration.Version, migration.File)

		err := executeMigrationFile(db, migration.File)
		if err != nil {
			log.Fatalf("Failed to apply migration %d: %v", migration.Version, err)
		}

		err = markMigrationApplied(db, migration.Version)
		if err != nil {
			log.Fatalf("Failed to mark migration %d as applied: %v", migration.Version, err)
		}

		fmt.Printf("Migration %d applied successfully\n", migration.Version)
	}

	fmt.Println("All UP migrations completed successfully!")
}

func runDownMigrations(db *sql.DB) {
	fmt.Println("Starting DOWN migrations...")

	for _, migration := range downMigrations {
		if !isMigrationApplied(db, migration.Version) {
			fmt.Printf("Migration %d not applied, skipping...\n", migration.Version)
			continue
		}

		fmt.Printf("Rolling back migration %d: %s\n", migration.Version, migration.File)

		err := executeMigrationFile(db, migration.File)
		if err != nil {
			log.Fatalf("Failed to rollback migration %d: %v", migration.Version, err)
		}

		err = markMigrationUnapplied(db, migration.Version)
		if err != nil {
			log.Fatalf("Failed to mark migration %d as unapplied: %v", migration.Version, err)
		}

		fmt.Printf("Migration %d rolled back successfully\n", migration.Version)
	}

	fmt.Println("All DOWN migrations completed successfully!")
}
