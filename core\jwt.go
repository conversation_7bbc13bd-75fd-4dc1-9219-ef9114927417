package core

import (
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// CustomClaims represents the custom claims for JWT token
type CustomClaims struct {
	UserID    uint64 `json:"user_id"`
	AccountID uint64 `json:"account_id"`
	jwt.RegisteredClaims
}

// JWTManager handles JWT token generation and validation
type JWTManager struct {
	secretKey []byte
	issuer    string
}

// NewJWTManager creates a new JWT manager with the provided secret key
func NewJWTManager(secretKey string, issuer string) *JWTManager {
	return &JWTManager{
		secretKey: []byte(secretKey),
		issuer:    issuer,
	}
}

// GenerateToken generates a new JWT token with user ID and account ID
func (j *J<PERSON>TManager) GenerateToken(userID, accountID uint64, expirationMinutes int) (string, error) {
	// Set expiration time
	expirationTime := time.Now().Add(time.Duration(expirationMinutes) * time.Minute)

	// Create custom claims
	claims := &CustomClaims{
		UserID:    userID,
		AccountID: accountID,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expirationTime),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    j.issuer,
		},
	}

	// Create token with claims
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// Sign token with secret key
	tokenString, err := token.SignedString(j.secretKey)
	if err != nil {
		return "", fmt.Errorf("failed to sign token: %w", err)
	}

	return tokenString, nil
}

// ValidateToken validates a JWT token and returns the custom claims
func (j *JWTManager) ValidateToken(tokenString string) (*CustomClaims, error) {
	// Parse token with custom claims
	token, err := jwt.ParseWithClaims(tokenString, &CustomClaims{}, func(token *jwt.Token) (interface{}, error) {
		// Validate signing method
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return j.secretKey, nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	// Extract custom claims
	if claims, ok := token.Claims.(*CustomClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, fmt.Errorf("invalid token claims")
}

// RefreshToken generates a new token with extended expiration time
func (j *JWTManager) RefreshToken(tokenString string, expirationMinutes int) (string, error) {
	// Validate existing token
	claims, err := j.ValidateToken(tokenString)
	if err != nil {
		return "", fmt.Errorf("invalid token for refresh: %w", err)
	}

	// Generate new token with same claims but extended expiration
	return j.GenerateToken(claims.UserID, claims.AccountID, expirationMinutes)
}
