package core

import (
	"testing"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

func TestJWTManager_GenerateToken(t *testing.T) {
	secretKey := "test-secret-key"
	issuer := "test-issuer"
	jwtManager := NewJWTManager(secretKey, issuer)

	userID := uint64(123)
	accountID := uint64(456)
	expirationMinutes := 15

	token, err := jwtManager.GenerateToken(userID, accountID, expirationMinutes)
	if err != nil {
		t.Fatalf("Failed to generate token: %v", err)
	}

	if token == "" {
		t.Error("Generated token is empty")
	}

	// Validate the generated token
	claims, err := jwtManager.ValidateToken(token)
	if err != nil {
		t.Fatalf("Failed to validate generated token: %v", err)
	}

	if claims.UserID != userID {
		t.<PERSON><PERSON>rf("Expected UserID %d, got %d", userID, claims.UserID)
	}

	if claims.AccountID != accountID {
		t.<PERSON><PERSON>("Expected AccountID %d, got %d", accountID, claims.AccountID)
	}

	if claims.Issuer != issuer {
		t.<PERSON><PERSON><PERSON>("Expected Issuer %s, got %s", issuer, claims.Issuer)
	}
}

func TestJWTManager_ValidateToken(t *testing.T) {
	secretKey := "test-secret-key"
	issuer := "test-issuer"
	jwtManager := NewJWTManager(secretKey, issuer)

	userID := uint64(789)
	accountID := uint64(101112)
	expirationMinutes := 15

	// Generate a token
	token, err := jwtManager.GenerateToken(userID, accountID, expirationMinutes)
	if err != nil {
		t.Fatalf("Failed to generate token: %v", err)
	}

	// Validate the token
	claims, err := jwtManager.ValidateToken(token)
	if err != nil {
		t.Fatalf("Failed to validate token: %v", err)
	}

	if claims.UserID != userID {
		t.Errorf("Expected UserID %d, got %d", userID, claims.UserID)
	}

	if claims.AccountID != accountID {
		t.Errorf("Expected AccountID %d, got %d", accountID, claims.AccountID)
	}
}

func TestJWTManager_ValidateInvalidToken(t *testing.T) {
	secretKey := "test-secret-key"
	issuer := "test-issuer"
	jwtManager := NewJWTManager(secretKey, issuer)

	// Test with invalid token
	invalidToken := "invalid.token.here"
	_, err := jwtManager.ValidateToken(invalidToken)
	if err == nil {
		t.Error("Expected error for invalid token, got nil")
	}
}

func TestJWTManager_ValidateExpiredToken(t *testing.T) {
	secretKey := "test-secret-key"
	issuer := "test-issuer"
	jwtManager := NewJWTManager(secretKey, issuer)

	// Create an expired token manually
	claims := &CustomClaims{
		UserID:    123,
		AccountID: 456,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(-1 * time.Hour)), // Expired 1 hour ago
			IssuedAt:  jwt.NewNumericDate(time.Now().Add(-2 * time.Hour)),
			NotBefore: jwt.NewNumericDate(time.Now().Add(-2 * time.Hour)),
			Issuer:    issuer,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(secretKey))
	if err != nil {
		t.Fatalf("Failed to create expired token: %v", err)
	}

	// Try to validate expired token
	_, err = jwtManager.ValidateToken(tokenString)
	if err == nil {
		t.Error("Expected error for expired token, got nil")
	}
}

func TestJWTManager_RefreshToken(t *testing.T) {
	secretKey := "test-secret-key"
	issuer := "test-issuer"
	jwtManager := NewJWTManager(secretKey, issuer)

	userID := uint64(123)
	accountID := uint64(456)
	expirationMinutes := 15

	// Generate original token
	originalToken, err := jwtManager.GenerateToken(userID, accountID, expirationMinutes)
	if err != nil {
		t.Fatalf("Failed to generate original token: %v", err)
	}

	// Wait a moment to ensure different timestamps
	time.Sleep(time.Millisecond * 10)

	// Refresh the token
	refreshedToken, err := jwtManager.RefreshToken(originalToken, expirationMinutes)
	if err != nil {
		t.Fatalf("Failed to refresh token: %v", err)
	}

	// Validate refreshed token
	claims, err := jwtManager.ValidateToken(refreshedToken)
	if err != nil {
		t.Fatalf("Failed to validate refreshed token: %v", err)
	}

	if claims.UserID != userID {
		t.Errorf("Expected UserID %d, got %d", userID, claims.UserID)
	}

	if claims.AccountID != accountID {
		t.Errorf("Expected AccountID %d, got %d", accountID, claims.AccountID)
	}

	// Both tokens should be valid (this is the important test)
	if refreshedToken == "" {
		t.Error("Refreshed token should not be empty")
	}
}
