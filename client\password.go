package client

import (
	"context"
	"fmt"

	"github.com/homewizeAI/authms/model"
)

func (c *Client) PwdCreate(ctx context.Context, data model.Password) (uint64, error) {
	url := c.UrlPfx + authPwd
	return c.Client.Client.Create(ctx, url, data, nil)
}

func (c *Client) PwdUpdate(ctx context.Context, id uint64, data model.Password) (uint64, error) {
	url := fmt.Sprintf("%s%s/%d", c.UrlPfx, authPwd, id)
	return c.Client.Client.Update(ctx, url, data, nil)
}

func (c *Client) PwdDelete(ctx context.Context, id uint64) (uint64, error) {
	url := fmt.Sprintf("%s%s/%d", c.UrlPfx, authPwd, id)
	return c.Client.Client.Delete(ctx, url, nil)
}

func (c *Client) PwdValidate(ctx context.Context, data model.PwdValidateRequest) (*model.Token, error) {
	url := c.UrlPfx + authPwdValidate
	var token model.Token
	err := c.Client.Client.DoRequest(ctx, "POST", url, data, nil, &token)
	if err != nil {
		return nil, err
	}
	return &token, nil
}

func (c *Client) PwdCheckStrength(ctx context.Context, data model.PwdStrengthRequest) (*model.PwdStrengthResponse, error) {
	url := c.UrlPfx + authPwdCheckStrength
	var result model.PwdStrengthResponse
	err := c.Client.Client.DoRequest(ctx, "POST", url, data, nil, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (c *Client) RefreshToken(ctx context.Context, data model.RefreshTokenRequest) (*model.RefreshTokenResponse, error) {
	url := c.UrlPfx + authTokenRefresh
	var result model.RefreshTokenResponse
	err := c.Client.Client.DoRequest(ctx, "POST", url, data, nil, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}
