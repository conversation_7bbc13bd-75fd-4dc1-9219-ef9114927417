package client

const (
	urlBase = "/auth"

	// Password URLs
	authPwd              = urlBase + "/pwd"
	authPwdValidate      = urlBase + "/pwd/validate"
	authPwdCheckStrength = urlBase + "/pwd/check-strength"

	// Password Reset URLs
	authPwdResetCreate       = urlBase + "/pwd/reset"
	authPwdResetValidate     = urlBase + "/pwd/reset/validate"
	authPwdResetDelete       = urlBase + "/pwd/reset"
	authPwdResetKeyGet       = urlBase + "/pwd/reset/key"
	authPwdResetUpdate       = urlBase + "/pwd/reset/update"
	authPwdResetHasActiveKey = urlBase + "/pwd/reset/has-active-key"

	// Token URLs
	authTokenRefresh      = urlBase + "/token/refresh"
	authTokenBelongsTo    = urlBase + "/token/belongs-to"
	authTokenRevoke       = urlBase + "/token/refresh"
	authTokenDeleteByUser = urlBase + "/token/refresh/user"
	authTokenFetchAccUsr  = urlBase + "/token/refresh/fetch-acc-usr"
)
