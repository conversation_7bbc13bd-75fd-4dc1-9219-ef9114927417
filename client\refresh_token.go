package client

import (
	"context"
	"fmt"

	"github.com/homewizeAI/apitypes"
	"github.com/homewizeAI/authms/model"
)

func (c *Client) RefreshTokenBelongsTo(ctx context.Context, data model.RefreshTokenBelongsToReq) (bool, error) {
	url := c.UrlPfx + authTokenBelongsTo
	var result apitypes.BoolType
	err := c.Client.Client.DoRequest(ctx, "POST", url, data, nil, &result)
	if err != nil {
		return false, err
	}
	return result.Value, nil
}

func (c *Client) RenewRefreshToken(ctx context.Context, data model.RefreshTokenRequest) (*model.RTokenRenewRes, error) {
	url := c.UrlPfx + authTokenRefresh
	var result model.RTokenRenewRes
	err := c.Client.Client.DoRequest(ctx, "POST", url, data, nil, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (c *Client) RevokeRefreshToken(ctx context.Context, data model.RTokenRevokeReq) (uint64, error) {
	url := c.UrlPfx + authTokenRevoke
	var result uint64
	err := c.Client.Client.DoRequest(ctx, "DELETE", url, data, nil, &result)
	if err != nil {
		return 0, err
	}
	return result, nil
}

func (c *Client) DeleteRefreshTokenByUser(ctx context.Context, userID uint64) (uint64, error) {
	url := fmt.Sprintf("%s%s/%d", c.UrlPfx, authTokenDeleteByUser, userID)
	var result uint64
	err := c.Client.Client.DoRequest(ctx, "DELETE", url, nil, nil, &result)
	if err != nil {
		return 0, err
	}
	return result, nil
}

func (c *Client) RTokenFetchAccUsr(ctx context.Context, data model.RTokenFetchAccUsrReq) (*model.RTokenFetchAccUsrRes, error) {
	url := c.UrlPfx + authTokenFetchAccUsr
	var result model.RTokenFetchAccUsrRes
	err := c.Client.Client.DoRequest(ctx, "POST", url, data, nil, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}
