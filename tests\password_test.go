package tests

import (
	"fmt"
	"net/http"
	"testing"
)

func TestPwdCreate(t *testing.T) {
	defer cleanupTestData()

	e := getExpect(t)
	password := createTestPassword(TestUserID, TestAccountID)

	// Test successful password creation
	jsonResp := e.POST("/auth/pwd").
		WithJSON(password).
		Expect().
		Status(http.StatusOK).
		JSON()

	// Verify response structure
	jsonResp.Object().ContainsKey("id")
	passwordID := uint64(jsonResp.Object().Value("id").Number().Raw())
	testData.PasswordIDs = append(testData.PasswordIDs, passwordID)

	// Test duplicate password creation (should fail)
	e.POST("/auth/pwd").
		WithJSON(password).
		Expect().
		Status(http.StatusConflict)
}

func TestPwdUpdate(t *testing.T) {
	defer cleanupTestData()

	e := getExpect(t)
	password := createTestPassword(TestUserID, TestAccountID)

	// Create password first
	resp := e.POST("/auth/pwd").
		WithJSON(password).
		Expect().
		Status(http.StatusOK).
		JSON()

	passwordID := uint64(resp.Object().Value("id").Number().Raw())
	testData.PasswordIDs = append(testData.PasswordIDs, passwordID)

	// Update password
	updatedPassword := createTestPassword(TestUserID, TestAccountID)
	updatedPassword.Pwd = "NewTestPassword123!"

	e.PUT(fmt.Sprintf("/auth/pwd/%d", passwordID)).
		WithJSON(updatedPassword).
		Expect().
		Status(http.StatusOK).
		JSON().Object().ContainsKey("affected_rows")

	// Test update non-existent password
	e.PUT("/auth/pwd/999999").
		WithJSON(updatedPassword).
		Expect().
		Status(http.StatusNotFound)
}

func TestPwdDelete(t *testing.T) {
	defer cleanupTestData()

	e := getExpect(t)
	password := createTestPassword(TestUserID, TestAccountID)

	// Create password first
	resp := e.POST("/auth/pwd").
		WithJSON(password).
		Expect().
		Status(http.StatusOK).
		JSON()

	passwordID := uint64(resp.Object().Value("id").Number().Raw())

	// Delete password
	e.DELETE(fmt.Sprintf("/auth/pwd/%d", passwordID)).
		Expect().
		Status(http.StatusOK).
		JSON().Object().ContainsKey("affected_rows")

	// Test delete non-existent password
	e.DELETE("/auth/pwd/999999").
		Expect().
		Status(http.StatusNotFound)
}

func TestPwdValidate(t *testing.T) {
	defer cleanupTestData()

	e := getExpect(t)
	password := createTestPassword(TestUserID, TestAccountID)

	// Create password first
	resp := e.POST("/auth/pwd").
		WithJSON(password).
		Expect().
		Status(http.StatusOK).
		JSON()

	passwordID := uint64(resp.Object().Value("id").Number().Raw())
	testData.PasswordIDs = append(testData.PasswordIDs, passwordID)

	// Test successful password validation
	validateReq := createTestPwdValidateRequest(TestUserID, TestPassword)
	resp = e.POST("/auth/pwd/validate").
		WithJSON(validateReq).
		Expect().
		Status(http.StatusOK).
		JSON()

	// Verify response contains tokens
	resp.Object().ContainsKey("access")
	resp.Object().ContainsKey("refresh")
	resp.Object().Value("access").String().NotEmpty()
	resp.Object().Value("refresh").String().NotEmpty()

	// Test invalid password
	invalidReq := createTestPwdValidateRequest(TestUserID, "WrongPassword")
	e.POST("/auth/pwd/validate").
		WithJSON(invalidReq).
		Expect().
		Status(http.StatusNotFound)

	// Test non-existent user
	nonExistentReq := createTestPwdValidateRequest(999999, TestPassword)
	e.POST("/auth/pwd/validate").
		WithJSON(nonExistentReq).
		Expect().
		Status(http.StatusNotFound)
}

func TestPwdCheckStrength(t *testing.T) {
	defer cleanupTestData()

	e := getExpect(t)

	// Test strong password
	strongReq := createTestPwdStrengthRequest("StrongPassword123!")
	resp := e.POST("/auth/pwd/check-strength").
		WithJSON(strongReq).
		Expect().
		Status(http.StatusOK).
		JSON()

	resp.Object().ContainsKey("strong")
	resp.Object().Value("strong").Boolean().IsTrue()

	// Test weak password
	weakReq := createTestPwdStrengthRequest("weak")
	resp = e.POST("/auth/pwd/check-strength").
		WithJSON(weakReq).
		Expect().
		Status(http.StatusOK).
		JSON()

	resp.Object().ContainsKey("strong")
	resp.Object().Value("strong").Boolean().IsFalse()
	resp.Object().ContainsKey("messages")

	// Test empty password
	emptyReq := createTestPwdStrengthRequest("")
	e.POST("/auth/pwd/check-strength").
		WithJSON(emptyReq).
		Expect().
		Status(http.StatusBadRequest)

	// Test password with only numbers
	numbersOnlyReq := createTestPwdStrengthRequest("123456789")
	resp = e.POST("/auth/pwd/check-strength").
		WithJSON(numbersOnlyReq).
		Expect().
		Status(http.StatusOK).
		JSON()

	resp.Object().ContainsKey("strong")
	resp.Object().Value("strong").Boolean().IsFalse()

	// Test password with only letters
	lettersOnlyReq := createTestPwdStrengthRequest("abcdefghijk")
	resp = e.POST("/auth/pwd/check-strength").
		WithJSON(lettersOnlyReq).
		Expect().
		Status(http.StatusOK).
		JSON()

	resp.Object().ContainsKey("strong")
	resp.Object().Value("strong").Boolean().IsFalse()

	// Test short password
	shortReq := createTestPwdStrengthRequest("Abc1!")
	resp = e.POST("/auth/pwd/check-strength").
		WithJSON(shortReq).
		Expect().
		Status(http.StatusOK).
		JSON()

	resp.Object().ContainsKey("strong")
	resp.Object().Value("strong").Boolean().IsFalse()
}

func TestPwdCreateInvalidPayload(t *testing.T) {
	defer cleanupTestData()

	e := getExpect(t)

	// Test with invalid JSON
	e.POST("/auth/pwd").
		WithText("invalid json").
		Expect().
		Status(http.StatusBadRequest)

	// Test with missing required fields
	e.POST("/auth/pwd").
		WithJSON(map[string]interface{}{
			"pwd": "TestPassword123!",
			// missing usr_id and acc_id
		}).
		Expect().
		Status(http.StatusBadRequest)
}

func TestPwdValidateInvalidPayload(t *testing.T) {
	defer cleanupTestData()

	e := getExpect(t)

	// Test with invalid JSON
	e.POST("/auth/pwd/validate").
		WithText("invalid json").
		Expect().
		Status(http.StatusBadRequest)

	// Test with missing required fields
	e.POST("/auth/pwd/validate").
		WithJSON(map[string]interface{}{
			"usr_id": TestUserID,
			// missing password
		}).
		Expect().
		Status(http.StatusBadRequest)

	// Test with missing usr_id
	e.POST("/auth/pwd/validate").
		WithJSON(map[string]interface{}{
			"password": TestPassword,
			// missing usr_id
		}).
		Expect().
		Status(http.StatusBadRequest)
}
