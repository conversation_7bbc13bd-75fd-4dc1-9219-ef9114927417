# Authentication Management Service (authms)

A comprehensive authentication management microservice built with Go and PostgreSQL, including password management functionality.

## Features

- Secure password management (create, update, delete only)
- Password validation and strength checking
- JWT token generation with custom claims (UserID, AccountID)
- Refresh token functionality with database persistence
- RESTful API with JSON responses
- PostgreSQL database with proper indexing and placeholders ($1, $2, etc.)
- Maximum security design - no password retrieval endpoints
- Go client library with circuit breaker support
- **Database Migrations**: Automated migration system with up/down support
- **Comprehensive Testing**: Unit tests, integration tests, and client tests
- **Production Ready**: Proper null handling, error management, and PostgreSQL optimization

## Database Schema

The service uses PostgreSQL with automated migrations. The database includes:

### Tables

**pwds table:**
```sql
CREATE TABLE pwds (
    id BIGSERIAL PRIMARY KEY,
    pwd VARCHAR(255) NOT NULL,
    acc_id BIGINT NOT NULL,
    usr_id BIGINT NOT NULL UNIQUE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

**refresh_tokens table:**
```sql
CREATE TABLE refresh_tokens (
    id BIGSERIAL PRIMARY KEY,
    token VARCHAR(255) NOT NULL UNIQUE,
    user_id BIGINT NOT NULL,
    acc_id BIGINT NOT NULL,
    valid_from TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

### Database Migrations

The service includes an automated migration system:

```bash
# Apply all migrations
./run-migrations.sh up

# Rollback all migrations
./run-migrations.sh down
```

**Migration Features:**
- Tracks applied migrations in `schema_migrations` table
- Supports both up and down migrations
- PostgreSQL optimized with proper data types and constraints
- Automatic indexing for performance

## API Endpoints

### Password Management
- `POST /auth/pwd` - Create a new password (with strength validation)
- `PUT /auth/pwd/:id` - Update password (with strength validation)
- `DELETE /auth/pwd/:id` - Delete password
- `POST /auth/pwd/validate` - Validate user password
- `POST /auth/pwd/check-strength` - Check password strength

**Maximum Security Design**: Password retrieval endpoints have been completely removed for security. Passwords are hashed using Argon2id before storage and can only be created, updated, deleted, or validated - never retrieved. All password creation and updates include automatic strength validation. This prevents any form of password exposure through the API.

#### Password Validation Endpoint

The validation endpoint accepts a JSON payload:
```json
{
  "usr_id": 123,
  "password": "plaintext_password"
}
```

And returns:
```json
{
  "valid": true
}
```

#### Password Strength Check Endpoint

The strength check endpoint accepts a JSON payload:
```json
{
  "password": "MyPassword123!"
}
```

And returns:
```json
{
  "strong": true,
  "messages": []
}
```

For weak passwords, it returns:
```json
{
  "strong": false,
  "messages": [
    "Password must be at least 8 characters long",
    "Password must contain at least one uppercase letter"
  ]
}
```

#### Password Strength Requirements

Passwords must meet the following criteria:
- At least 8 characters long
- Contains at least one uppercase letter (A-Z)
- Contains at least one lowercase letter (a-z)
- Contains at least one digit (0-9)
- Contains at least one special character (!@#$%^&*()_+-=[]{}|;':"\\,.<>?/)
- Does not contain common weak patterns (password, 123456, qwerty, etc.)

## Running the Service

### Local Development

1. Set up PostgreSQL database
2. Run migrations from `db/migrations/`
3. Configure database connection in `conf/secrets.toml`
4. Run the service:
   ```bash
   go run main.go
   ```
   Or use the deployment script:
   ```bash
   ./deploy.sh
   ```

### Docker Deployment

1. Build Docker images:
   ```bash
   ./dkr-bld.sh
   ```

2. Run with Docker Compose or manually:
   ```bash
   # Run migrations first
   docker run --rm -v $(pwd)/conf:/app/conf github.com/homewizeAI/authms-migrate

   # Run the service
   docker run -p 3001:3001 -v $(pwd)/conf:/app/conf github.com/homewizeAI/authms
   ```

The service will start on port 3001 by default.

## JWT Configuration

Configure JWT settings in `conf/authkeys.toml`:

```toml
[jwt]
# JWT Token signing key - should be a strong secret key in production
token_sign_key = "your-super-secret-jwt-signing-key-change-this-in-production"

# JWT Token issuer
issuer = "authms"

# JWT Token expiration time in minutes (default: 15 minutes)
expiration_minutes = 15

# JWT Token refresh expiration time in hours (default: 168 hours = 7 days)
refresh_expiration_hours = 168
```

### JWT Token Response

When password validation is successful, the API returns a Token object with JWT access token and refresh token:

```json
{
  "valid": true,
  "access": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh": "secure-64-char-random-string-here"
}
```

The JWT token contains:
- `user_id`: User ID from the validation request
- `account_id`: Account ID associated with the user
- `iss`: Token issuer (configured in authkeys.toml)
- `exp`: Token expiration time
- `iat`: Token issued at time
- `nbf`: Token not before time

### Refresh Token Functionality

Refresh tokens are securely generated 64-character strings stored in the database with:
- `token`: Secure random string generated using crypto/rand
- `user_id`: Associated user ID
- `acc_id`: Associated account ID
- `valid_from`: Token validity start time

**Simple Design**: No expiration time - tokens are valid until explicitly revoked or replaced.

#### Refresh Token API

**Endpoint:** `POST /auth/token/refresh`

**Request:**
```json
{
  "refresh_token": "your-refresh-token-here"
}
```

**Response:**
```json
{
  "access_token": "new-jwt-token",
  "refresh_token": "new-refresh-token",
  "expires_in": 900
}
```

**Features:**
- Validates existing refresh token
- Generates new JWT and refresh token pair
- Revokes old refresh token for security
- Simple design without complex expiration logic

## Go Client Library

The `client/` directory contains a Go client library for interacting with the authms service.

### Client Features

- Password management operations (create, update, delete) using clientcommon v0.0.5
- Password validation and strength checking using clientcommon DoRequest
- Circuit breaker support for all operations
- Comprehensive test coverage

### Client Usage

```go
import "github.com/homewizeAI/authms/client"

// Initialize client
c := client.NewWithURLPfx("http://localhost:3001")

// Create password
pwd := model.Password{
    Pwd:   "SecurePassword123!",
    AccID: 1,
    UsrID: 1,
}
id, err := c.PwdCreate(ctx, pwd)

// Validate password
validateReq := model.PwdValidateRequest{
    UsrID:    1,
    Password: "SecurePassword123!",
}
token, err := c.PwdValidate(ctx, validateReq)
if token.Valid {
    // Use token.Access for JWT and token.Refresh for refresh token
}

// Check password strength
strengthReq := model.PwdStrengthRequest{
    Password: "MyPassword123!",
}
result, err := c.PwdCheckStrength(ctx, strengthReq)

// Refresh JWT token using refresh token
refreshReq := model.RefreshTokenRequest{
    RefreshToken: "your-refresh-token-here",
}
refreshResult, err := c.RefreshToken(ctx, refreshReq)
```

### Client Testing

```bash
cd client && go test -v
```

## Testing

The service includes comprehensive test coverage:

### Test Types

1. **Unit Tests** - DAO layer and core logic tests
2. **Integration Tests** - API endpoint tests
3. **Client Tests** - Client library tests
4. **Core Tests** - JWT and refresh token logic tests

### Running Tests

```bash
# Run all available tests
./run-tests.sh all

# Run specific test types
./run-tests.sh client    # Client library tests
./run-tests.sh unit      # Unit tests (requires database)
./run-tests.sh core      # Core logic tests

# Run individual test suites
go test ./client -v      # Client tests
go test ./core -v        # Core tests
go test ./tests -v       # Database tests (requires setup)
```

### Test Database Setup

For unit and integration tests that require database access:

1. Create test database:
   ```sql
   CREATE DATABASE authms_test;
   ```

2. Run tests:
   ```bash
   go test ./tests -v
   ```

**Test Features:**
- Automatic test database setup and cleanup
- Comprehensive DAO testing with PostgreSQL
- API endpoint testing with mock HTTP requests
- Client library testing with httpmock
- Error handling and edge case coverage

## Build Scripts

- `build.sh` - Dependency management and build verification script
- `deploy.sh` - Local deployment script (runs with go run)
- `dkr-bld.sh` - Docker build script
- `run-migrations.sh` - Database migration runner
- `run-tests.sh` - Comprehensive test runner