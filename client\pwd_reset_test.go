package client

import (
	"context"
	"net/http"
	"testing"

	"github.com/homewizeAI/authms/model"
	"github.com/jarcoal/httpmock"
	"github.com/stretchr/testify/assert"
)

func TestPwdResetOperations(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	// Mock Create
	httpmock.RegisterResponder(
		"POST",
		"http://localhost:8080/auth/pwd/reset",
		func(req *http.Request) (*http.Response, error) {
			resp := model.PwdResetCreateResponse{
				ResetKey: "test-reset-key",
				ID:       1,
			}
			return httpmock.NewJsonResponse(http.StatusOK, resp)
		},
	)

	// Mock Validate
	httpmock.RegisterResponder(
		"POST",
		"http://localhost:8080/auth/pwd/reset/validate",
		func(req *http.Request) (*http.Response, error) {
			resp := model.PwdResetValidateResponse{
				Valid: true,
				PwdID: 1,
			}
			return httpmock.NewJsonResponse(http.StatusOK, resp)
		},
	)

	// Mock Key Get
	httpmock.RegisterResponder(
		"POST",
		"http://localhost:8080/auth/pwd/reset/key",
		func(req *http.Request) (*http.Response, error) {
			resp := model.PwdResetKeyGetResponse{
				AccID: 1,
				UsrID: 123,
			}
			return httpmock.NewJsonResponse(http.StatusOK, resp)
		},
	)

	// Mock Has Active Key
	httpmock.RegisterResponder(
		"GET",
		"http://localhost:8080/auth/pwd/reset/has-active-key/123",
		func(req *http.Request) (*http.Response, error) {
			return httpmock.NewJsonResponse(http.StatusOK, true)
		},
	)

	// Mock Update
	httpmock.RegisterResponder(
		"PUT",
		"http://localhost:8080/auth/pwd/reset/update",
		func(req *http.Request) (*http.Response, error) {
			return httpmock.NewJsonResponse(http.StatusOK, uint64(1))
		},
	)

	// Mock Delete
	httpmock.RegisterResponder(
		"DELETE",
		"http://localhost:8080/auth/pwd/reset/delete",
		func(req *http.Request) (*http.Response, error) {
			return httpmock.NewJsonResponse(http.StatusOK, uint64(1))
		},
	)

	ctx := context.Background()
	client := NewWithURLPfx("http://localhost:8080")

	// Test Create
	createReq := model.PwdResetCreateRequest{
		UsrID: 123,
	}
	createResp, err := client.PwdResetCreate(ctx, createReq)
	assert.NoError(t, err)
	assert.NotEmpty(t, createResp.ResetKey)
	assert.NotZero(t, createResp.ID)

	// Test Validate
	validateReq := model.PwdResetValidateRequest{
		ResetKey: createResp.ResetKey,
	}
	validateResp, err := client.PwdResetValidate(ctx, validateReq)
	assert.NoError(t, err)
	assert.True(t, validateResp.Valid)
	assert.NotZero(t, validateResp.PwdID)

	// Test Key Get
	keyGetReq := model.PwdResetKeyGetRequest{
		ResetKey: createResp.ResetKey,
		ID:       createResp.ID,
	}
	keyGetResp, err := client.PwdResetKeyGet(ctx, keyGetReq)
	assert.NoError(t, err)
	assert.NotZero(t, keyGetResp.UsrID)
	assert.NotZero(t, keyGetResp.AccID)

	// Test Has Active Key
	hasKey, err := client.PwdResetHasActiveKey(ctx, createReq.UsrID)
	assert.NoError(t, err)
	assert.True(t, hasKey)

	// Test Update
	updateReq := model.PwdResetUpdateRequest{
		ResetKey: createResp.ResetKey,
		ID:       createResp.ID,
		Pwd:      "newStrongPassword123!",
	}
	rowsAffected, err := client.PwdResetUpdate(ctx, updateReq)
	assert.NoError(t, err)
	assert.Equal(t, uint64(1), rowsAffected)

	// Test Delete
	deleteReq := model.PwdResetDeleteRequest{
		ResetKey: createResp.ResetKey,
		ID:       createResp.ID,
	}
	rowsAffected, err = client.PwdResetDelete(ctx, deleteReq)
	assert.NoError(t, err)
	assert.Equal(t, uint64(1), rowsAffected)

	// Verify key no longer active
	hasKey, err = client.PwdResetHasActiveKey(ctx, createReq.UsrID)
	assert.NoError(t, err)
	assert.True(t, hasKey) // Note: This is mocked to always return true
}
