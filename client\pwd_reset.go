package client

import (
	"context"
	"fmt"

	"github.com/homewizeAI/apitypes"
	"github.com/homewizeAI/authms/model"
)

func (c *Client) PwdResetCreate(ctx context.Context, data model.PwdResetCreateRequest) (*model.PwdResetCreateResponse, error) {
	url := c.UrlPfx + authPwdResetCreate
	var result model.PwdResetCreateResponse
	err := c.Client.Client.DoRequest(ctx, "POST", url, data, nil, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (c *Client) PwdResetValidate(ctx context.Context, data model.PwdResetValidateRequest) (*model.PwdResetValidateResponse, error) {
	url := c.UrlPfx + authPwdResetValidate
	var result model.PwdResetValidateResponse
	err := c.Client.Client.DoRequest(ctx, "POST", url, data, nil, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (c *Client) PwdResetDelete(ctx context.Context, data model.PwdResetDeleteRequest) (uint64, error) {
	url := c.UrlPfx + authPwdResetDelete
	var result uint64
	err := c.Client.Client.DoRequest(ctx, "DELETE", url, data, nil, &result)
	if err != nil {
		return 0, err
	}
	return result, nil
}

func (c *Client) PwdResetKeyGet(ctx context.Context, data model.PwdResetKeyGetRequest) (*model.PwdResetKeyGetResponse, error) {
	url := c.UrlPfx + authPwdResetKeyGet
	var result model.PwdResetKeyGetResponse
	err := c.Client.Client.DoRequest(ctx, "POST", url, data, nil, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (c *Client) PwdResetUpdate(ctx context.Context, data model.PwdResetUpdateRequest) (uint64, error) {
	url := c.UrlPfx + authPwdResetUpdate
	var result uint64
	err := c.Client.Client.DoRequest(ctx, "PUT", url, data, nil, &result)
	if err != nil {
		return 0, err
	}
	return result, nil
}

func (c *Client) PwdResetHasActiveKey(ctx context.Context, usrID uint64) (bool, error) {
	url := fmt.Sprintf("%s%s/%d", c.UrlPfx, authPwdResetHasActiveKey, usrID)
	var result apitypes.BoolType
	err := c.Client.Client.DoRequest(ctx, "GET", url, nil, nil, &result)
	if err != nil {
		return false, err
	}
	return result.Value, nil
}
